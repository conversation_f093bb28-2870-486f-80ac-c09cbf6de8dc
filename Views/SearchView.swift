//
//  SearchView.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import SwiftUI

struct SearchView: View {
    @EnvironmentObject var countryDataService: CountryDataService
    @State private var searchText = ""
    @State private var selectedSafetyFilter: SafetyRating?
    @State private var selectedContinent: String?
    @State private var showingFilters = false
    
    // Debounced search
    @State private var debouncedSearchText = ""
    @State private var searchTask: Task<Void, Never>?
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // Search Bar and Filters
                VStack(spacing: 12) {
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.secondary)
                        
                        TextField("Search countries...", text: $searchText)
                            .textFieldStyle(.plain)
                            .onChange(of: searchText) { _, newValue in
                                debounceSearch(newValue)
                            }
                        
                        if !searchText.isEmpty {
                            Button {
                                searchText = ""
                                debouncedSearchText = ""
                            } label: {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    
                    // Filter Chips
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 8) {
                            FilterChip(
                                title: "Filters",
                                isSelected: showingFilters,
                                systemImage: "line.3.horizontal.decrease.circle"
                            ) {
                                showingFilters.toggle()
                            }
                            
                            if let continent = selectedContinent {
                                FilterChip(
                                    title: continent,
                                    isSelected: true,
                                    systemImage: "location"
                                ) {
                                    selectedContinent = nil
                                }
                            }
                            
                            if let safety = selectedSafetyFilter {
                                FilterChip(
                                    title: safety.displayName,
                                    isSelected: true,
                                    systemImage: "shield"
                                ) {
                                    selectedSafetyFilter = nil
                                }
                            }
                        }
                        .padding(.horizontal)
                    }
                }
                .padding()
                
                // Results
                if countryDataService.isLoading {
                    Spacer()
                    ProgressView("Loading countries...")
                    Spacer()
                } else if filteredCountries.isEmpty && !debouncedSearchText.isEmpty {
                    ContentUnavailableView.search(text: debouncedSearchText)
                } else if filteredCountries.isEmpty {
                    ContentUnavailableView(
                        "Explore Countries",
                        systemImage: "globe",
                        description: Text("Search for countries to view safety information and travel advisories.")
                    )
                } else {
                    List(filteredCountries) { country in
                        NavigationLink(destination: CountryDetailView(country: country)) {
                            CountryRowView(country: country)
                        }
                    }
                    .listStyle(.plain)
                }
            }
            .navigationTitle("Search")
            .sheet(isPresented: $showingFilters) {
                FilterSheet(
                    selectedSafetyFilter: $selectedSafetyFilter,
                    selectedContinent: $selectedContinent,
                    availableContinents: countryDataService.continents
                )
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var filteredCountries: [Country] {
        var countries = countryDataService.countries
        
        // Apply search filter
        if !debouncedSearchText.isEmpty {
            countries = countryDataService.searchCountries(query: debouncedSearchText)
        }
        
        // Apply safety filter
        if let safetyFilter = selectedSafetyFilter {
            countries = countries.filter { $0.overallSafetyRating == safetyFilter }
        }
        
        // Apply continent filter
        if let continent = selectedContinent {
            countries = countries.filter { $0.continent == continent }
        }
        
        return countries.sorted { $0.name < $1.name }
    }
    
    // MARK: - Methods
    
    private func debounceSearch(_ text: String) {
        searchTask?.cancel()
        searchTask = Task {
            let nanoseconds = UInt64(Constants.Data.searchDebounceDelay * 1_000_000_000)
            try? await Task.sleep(nanoseconds: nanoseconds)
            if !Task.isCancelled {
                await MainActor.run {
                    debouncedSearchText = text
                }
            }
        }
    }
}

// MARK: - Filter Chip View
struct FilterChip: View {
    let title: String
    let isSelected: Bool
    let systemImage: String?
    let action: () -> Void
    
    init(title: String, isSelected: Bool, systemImage: String? = nil, action: @escaping () -> Void) {
        self.title = title
        self.isSelected = isSelected
        self.systemImage = systemImage
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 4) {
                if let systemImage = systemImage {
                    Image(systemName: systemImage)
                        .font(.caption)
                }
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(isSelected ? Color.accentColor : Color(.systemGray5))
            .foregroundColor(isSelected ? .white : .primary)
            .cornerRadius(16)
        }
    }
}

// MARK: - Country Row View
struct CountryRowView: View {
    let country: Country
    
    var body: some View {
        HStack(spacing: 12) {
            // Flag
            Text(country.flagEmoji)
                .font(.largeTitle)
                .frame(width: 50, height: 50)
                .background(Color.gray.opacity(0.1))
                .clipShape(Circle())
            
            // Country Info
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(country.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Spacer()
                    
                    SafetyBadge(rating: country.overallSafetyRating)
                }
                
                Text(country.continent)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                if country.alerts.contains(where: { $0.isActive }) {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.orange)
                            .font(.caption)
                        Text("Active alerts")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
            }
            
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Safety Badge View
struct SafetyBadge: View {
    let rating: SafetyRating
    
    var body: some View {
        Text(rating.displayName)
            .font(.caption)
            .fontWeight(.medium)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color(rating.color).opacity(0.2))
            .foregroundColor(Color(rating.color))
            .cornerRadius(8)
    }
}

// MARK: - Filter Sheet
struct FilterSheet: View {
    @Binding var selectedSafetyFilter: SafetyRating?
    @Binding var selectedContinent: String?
    let availableContinents: [String]
    
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            List {
                Section("Safety Rating") {
                    ForEach(SafetyRating.allCases, id: \.self) { rating in
                        HStack {
                            Text(rating.displayName)
                            Spacer()
                            if selectedSafetyFilter == rating {
                                Image(systemName: "checkmark")
                                    .foregroundColor(.accentColor)
                            }
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            selectedSafetyFilter = selectedSafetyFilter == rating ? nil : rating
                        }
                    }
                }
                
                Section("Continent") {
                    ForEach(availableContinents, id: \.self) { continent in
                        HStack {
                            Text(continent)
                            Spacer()
                            if selectedContinent == continent {
                                Image(systemName: "checkmark")
                                    .foregroundColor(.accentColor)
                            }
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            selectedContinent = selectedContinent == continent ? nil : continent
                        }
                    }
                }
            }
            .navigationTitle("Filters")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Clear All") {
                        selectedSafetyFilter = nil
                        selectedContinent = nil
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

//#Preview {
//    SearchView()
//        .environmentObject(CountryDataService(modelContext: ModelContext(try! ModelContainer(for: Country.self, Trip.self, TravelNotification.self, inMemory: true))))
//}
