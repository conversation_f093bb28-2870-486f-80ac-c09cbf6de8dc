//
//  TripsView.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import SwiftUI
import SwiftData

struct TripsView: View {
    @Environment(\.modelContext) private var modelContext
    @EnvironmentObject var countryDataService: CountryDataService
    @Query(sort: [SortDescriptor(\Trip.startDate, order: .reverse)]) 
    private var trips: [Trip]
    
    @State private var selectedSegment: TripSegment = .active
    @State private var showingAddTrip = false
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // Segmented Control
                Picker("Trip Status", selection: $selectedSegment) {
                    ForEach(TripSegment.allCases, id: \.self) { segment in
                        Text(segment.displayName).tag(segment)
                    }
                }
                .pickerStyle(.segmented)
                .padding()
                
                // Trips List
                if filteredTrips.isEmpty {
                    VStack(spacing: 24) {
                        Image(systemName: emptyStateIcon)
                            .font(.system(size: 48))
                            .foregroundColor(.secondary)
                        Text(emptyStateTitle)
                            .font(.title2.bold())
                        VStack(spacing: 8) {
                            Text(emptyStateDescription)
                                .multilineTextAlignment(.center)
                                .foregroundColor(.secondary)
                            if selectedSegment == .active || selectedSegment == .upcoming {
                                Button("Add Your First Trip") {
                                    showingAddTrip = true
                                }
                                .buttonStyle(.borderedProminent)
                            }
                        }
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .padding()
                } else {
                    List {
                        ForEach(filteredTrips) { trip in
                            NavigationLink(destination: TripDetailView(trip: trip)) {
                                TripRowView(trip: trip)
                            }
                            .swipeActions(edge: .trailing) {
                                Button("Delete") {
                                    deleteTrip(trip)
                                }
                                .tint(.red)
                            }
                        }
                    }
                    .listStyle(.plain)
                }
            }
            .navigationTitle("My Trips")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showingAddTrip = true
                    } label: {
                        Image(systemName: "plus")
                    }
                }
            }
        }
        .sheet(isPresented: $showingAddTrip) {
            AddTripView()
        }
        .onAppear {
            updateTripStatuses()
        }
    }
    
    // MARK: - Computed Properties
    
    private var filteredTrips: [Trip] {
        switch selectedSegment {
        case .active:
            return trips.filter { $0.isActive }
        case .upcoming:
            return trips.filter { $0.isUpcoming }
        case .past:
            return trips.filter { $0.isPast }
        }
    }
    
    private var emptyStateTitle: String {
        switch selectedSegment {
        case .active: return "No Active Trips"
        case .upcoming: return "No Upcoming Trips"
        case .past: return "No Past Trips"
        }
    }
    
    private var emptyStateIcon: String {
        switch selectedSegment {
        case .active: return "airplane.departure"
        case .upcoming: return "clock"
        case .past: return "checkmark.circle"
        }
    }
    
    private var emptyStateDescription: String {
        switch selectedSegment {
        case .active: return "You don't have any active trips right now."
        case .upcoming: return "Plan your next adventure by adding a trip."
        case .past: return "Your completed trips will appear here."
        }
    }
    
    // MARK: - Actions
    
    private func deleteTrip(_ trip: Trip) {
        modelContext.delete(trip)
        try? modelContext.save()
    }
    
    private func updateTripStatuses() {
        for trip in trips {
            trip.updateStatus()
        }
        try? modelContext.save()
    }
}

// MARK: - Trip Segment Enum
enum TripSegment: String, CaseIterable {
    case active = "active"
    case upcoming = "upcoming"
    case past = "past"
    
    var displayName: String {
        switch self {
        case .active: return "Active"
        case .upcoming: return "Upcoming"
        case .past: return "Past"
        }
    }
}

// MARK: - Trip Row View
struct TripRowView: View {
    let trip: Trip
    
    var body: some View {
        HStack(spacing: 12) {
            // Country Flag
            Text(trip.countryFlag)
                .font(.largeTitle)
                .frame(width: 50, height: 50)
                .background(Color.gray.opacity(0.1))
                .clipShape(Circle())
            
            // Trip Details
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(trip.countryName)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Spacer()
                    
                    Image(systemName: trip.status.iconName)
                        .foregroundColor(Color(trip.status.color))
                        .font(.caption)
                }
                
                Text(trip.shortDateRange)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                HStack {
                    Image(systemName: trip.purpose.iconName)
                        .font(.caption)
                    Text(trip.purpose.displayName)
                        .font(.caption)
                    
                    Spacer()
                    
                    Text(trip.durationText)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Text(trip.statusText)
                    .font(.caption)
                    .foregroundColor(Color(trip.status.color))
                    .fontWeight(.medium)
            }
        }
        .padding(.vertical, 4)
    }
}

//#Preview {
//    TripsView()
//        .environmentObject(CountryDataService(modelContext: ModelContext(try! ModelContainer(for: Country.self, Trip.self, TravelNotification.self, inMemory: true))))
//        .modelContainer(for: [Trip.self], inMemory: true)
//}

