//
//  OptimizedCountryList.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import SwiftUI

/// Performance-optimized country list with lazy loading and image preloading
struct OptimizedCountryList: View {
    let countries: [Country]
    let onCountryTap: (Country) -> Void
    
    @StateObject private var imagePreloader = ImagePreloader()
    @State private var visibleCountries: Set<String> = []
    
    // Performance configuration
    private let batchSize = 20
    private let preloadDistance = 5
    
    var body: some View {
        LazyVStack(spacing: 0) {
            ForEach(Array(countries.enumerated()), id: \.element.id) { index, country in
                OptimizedCountryRow(
                    country: country,
                    onTap: { onCountryTap(country) }
                )
                .onAppear {
                    handleCountryAppear(country: country, index: index)
                }
                .onDisappear {
                    handleCountryDisappear(country: country)
                }
                .trackPerformance("country_row_\(country.id)")
                
                if index < countries.count - 1 {
                    Divider()
                        .padding(.leading, 74) // Align with content
                }
            }
        }
        .onAppear {
            preloadInitialImages()
        }
        .onDisappear {
            imagePreloader.cancelPreloading()
        }
    }
    
    // MARK: - Private Methods
    
    private func handleCountryAppear(country: Country, index: Int) {
        visibleCountries.insert(country.id)
        
        // Preload images for upcoming countries
        let preloadStartIndex = max(0, index - preloadDistance)
        let preloadEndIndex = min(countries.count - 1, index + preloadDistance)
        
        let urlsToPreload = (preloadStartIndex...preloadEndIndex).compactMap { i in
            countries[i].photoURL
        }
        
        imagePreloader.preloadImages(urls: urlsToPreload, priority: .background)
    }
    
    private func handleCountryDisappear(country: Country) {
        visibleCountries.remove(country.id)
    }
    
    private func preloadInitialImages() {
        let initialBatch = Array(countries.prefix(batchSize))
        let initialUrls = initialBatch.compactMap { $0.photoURL }
        
        imagePreloader.preloadImages(urls: initialUrls, priority: .userInitiated)
    }
}

// MARK: - Optimized Country Row
struct OptimizedCountryRow: View {
    let country: Country
    let onTap: () -> Void
    
    @State private var isImageLoaded = false
    
    var body: some View {
        NavigationLink(destination: LazyView(CountryDetailView(country: country))) {
            HStack(spacing: 12) {
                // Flag with optimized loading
                AsyncImage(url: URL(string: country.photoURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .onAppear {
                            withAnimation(.gentleFade) {
                                isImageLoaded = true
                            }
                        }
                } placeholder: {
                    if isImageLoaded {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Text(country.flagEmoji)
                            .font(.title)
                    }
                }
                .frame(width: 50, height: 50)
                .background(Color.gray.opacity(0.1))
                .clipShape(Circle())
                
                // Country info
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(country.name)
                            .font(.headline)
                            .fontWeight(.medium)
                            .lineLimit(1)
                        
                        Spacer()
                        
                        SafetyBadgeOptimized(rating: country.safetyRating)
                    }
                    
                    Text(country.continent)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                    
                    if !country.activeAlerts.isEmpty {
                        HStack(spacing: 4) {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .font(.caption)
                                .foregroundColor(.orange)
                            
                            Text("\(country.activeAlerts.count) active alert\(country.activeAlerts.count == 1 ? "" : "s")")
                                .font(.caption)
                                .foregroundColor(.orange)
                        }
                    }
                }
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 8)
            .padding(.horizontal, 16)
            .contentShape(Rectangle())
        }
        .onTapGesture {
            onTap()
        }
    }
}

// MARK: - Optimized Safety Badge
struct SafetyBadgeOptimized: View {
    let rating: SafetyRating
    
    var body: some View {
        Text(rating.displayName)
            .font(.caption)
            .fontWeight(.medium)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color(rating.color).opacity(0.2))
            .foregroundColor(Color(rating.color))
            .cornerRadius(8)
    }
}

// MARK: - Virtualized List
struct VirtualizedCountryList: View {
    let countries: [Country]
    let onCountryTap: (Country) -> Void
    
    @State private var scrollOffset: CGFloat = 0
    @State private var visibleRange: Range<Int> = 0..<20
    
    private let itemHeight: CGFloat = 80
    private let bufferSize = 10
    
    var body: some View {
        GeometryReader { geometry in
            ScrollView {
                LazyVStack(spacing: 0) {
                    // Top spacer for items above visible range
                    if visibleRange.lowerBound > 0 {
                        Rectangle()
                            .fill(Color.clear)
                            .frame(height: CGFloat(visibleRange.lowerBound) * itemHeight)
                    }
                    
                    // Visible items
                    ForEach(visibleItems, id: \.id) { country in
                        OptimizedCountryRow(
                            country: country,
                            onTap: { onCountryTap(country) }
                        )
                        .frame(height: itemHeight)
                    }
                    
                    // Bottom spacer for items below visible range
                    if visibleRange.upperBound < countries.count {
                        Rectangle()
                            .fill(Color.clear)
                            .frame(height: CGFloat(countries.count - visibleRange.upperBound) * itemHeight)
                    }
                }
                .background(
                    GeometryReader { scrollGeometry in
                        Color.clear
                            .preference(
                                key: ScrollOffsetPreferenceKey.self,
                                value: scrollGeometry.frame(in: .named("scroll")).minY
                            )
                    }
                )
            }
            .coordinateSpace(name: "scroll")
            .onPreferenceChange(ScrollOffsetPreferenceKey.self) { offset in
                updateVisibleRange(scrollOffset: -offset, viewHeight: geometry.size.height)
            }
        }
    }
    
    private var visibleItems: ArraySlice<Country> {
        let startIndex = max(0, visibleRange.lowerBound)
        let endIndex = min(countries.count, visibleRange.upperBound)
        return countries[startIndex..<endIndex]
    }
    
    private func updateVisibleRange(scrollOffset: CGFloat, viewHeight: CGFloat) {
        let firstVisibleIndex = max(0, Int(scrollOffset / itemHeight) - bufferSize)
        let lastVisibleIndex = min(
            countries.count,
            Int((scrollOffset + viewHeight) / itemHeight) + bufferSize
        )
        
        let newRange = firstVisibleIndex..<lastVisibleIndex
        
        if newRange != visibleRange {
            visibleRange = newRange
        }
    }
}

// MARK: - Scroll Offset Preference Key
struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

// MARK: - Performance Optimized Search Results
struct OptimizedSearchResults: View {
    let countries: [Country]
    let searchText: String
    let onCountryTap: (Country) -> Void
    
    @State private var displayedCountries: [Country] = []
    @State private var isLoadingMore = false
    
    private let batchSize = 20
    
    var body: some View {
        LazyVStack(spacing: 0) {
            ForEach(Array(displayedCountries.enumerated()), id: \.element.id) { index, country in
                OptimizedCountryRow(
                    country: country,
                    onTap: { onCountryTap(country) }
                )
                .onAppear {
                    if index == displayedCountries.count - 5 {
                        loadMoreCountries()
                    }
                }
                
                if index < displayedCountries.count - 1 {
                    Divider()
                        .padding(.leading, 74)
                }
            }
            
            if isLoadingMore && displayedCountries.count < countries.count {
                HStack {
                    Spacer()
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("Loading more...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Spacer()
                }
                .padding()
            }
        }
        .onAppear {
            loadInitialCountries()
        }
        .onChange(of: countries) { _, newCountries in
            loadInitialCountries()
        }
    }
    
    private func loadInitialCountries() {
        displayedCountries = Array(countries.prefix(batchSize))
    }
    
    private func loadMoreCountries() {
        guard !isLoadingMore && displayedCountries.count < countries.count else { return }
        
        isLoadingMore = true
        
        // Simulate async loading with small delay for smooth UX
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            let nextBatch = countries.dropFirst(displayedCountries.count).prefix(batchSize)
            displayedCountries.append(contentsOf: nextBatch)
            isLoadingMore = false
        }
    }
}

// MARK: - Memory Efficient Image View
struct MemoryEfficientImageView: View {
    let url: String?
    let placeholder: String
    let size: CGSize
    
    @StateObject private var imageLoader = ImageLoader()
    
    var body: some View {
        Group {
            if let image = imageLoader.image {
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } else if imageLoader.isLoading {
                ProgressView()
                    .scaleEffect(0.8)
            } else {
                Text(placeholder)
                    .font(.title)
            }
        }
        .frame(width: size.width, height: size.height)
        .background(Color.gray.opacity(0.1))
        .clipShape(Circle())
        .onAppear {
            if let url = url {
                imageLoader.loadImage(from: url)
            }
        }
        .onDisappear {
            // Clear image from memory when not visible
            imageLoader.image = nil
        }
    }
}
