//
//  SkeletonViews.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import SwiftUI

// MARK: - Skeleton Loading Views
struct SkeletonView: View {
    let width: CGFloat?
    let height: CGFloat
    let cornerRadius: CGFloat
    
    init(width: CGFloat? = nil, height: CGFloat = 20, cornerRadius: CGFloat = 4) {
        self.width = width
        self.height = height
        self.cornerRadius = cornerRadius
    }
    
    var body: some View {
        Rectangle()
            .fill(Color.gray.opacity(0.3))
            .frame(width: width, height: height)
            .cornerRadius(cornerRadius)
            .overlay(ShimmerEffect())
            .clipped()
    }
}

// MARK: - Country Row Skeleton
struct CountryRowSkeleton: View {
    var body: some View {
        HStack(spacing: 12) {
            // Flag placeholder
            Circle()
                .fill(Color.gray.opacity(0.3))
                .frame(width: 50, height: 50)
                .overlay(ShimmerEffect())
            
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Skel<PERSON><PERSON>ie<PERSON>(width: 120, height: 18)
                    Spacer()
                    SkeletonView(width: 60, height: 16, cornerRadius: 8)
                }
                
                SkeletonView(width: 80, height: 14)
                
                SkeletonView(width: 100, height: 12)
            }
            
            SkeletonView(width: 8, height: 12)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Trip Card Skeleton
struct TripCardSkeleton: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Circle()
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 40, height: 40)
                    .overlay(ShimmerEffect())
                
                VStack(alignment: .leading, spacing: 4) {
                    SkeletonView(width: 100, height: 16)
                    SkeletonView(width: 60, height: 12)
                }
                
                Spacer()
                
                SkeletonView(width: 50, height: 14, cornerRadius: 8)
            }
            
            SkeletonView(width: 150, height: 12)
            SkeletonView(width: 120, height: 12)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Notification Row Skeleton
struct NotificationRowSkeleton: View {
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Circle()
                .fill(Color.gray.opacity(0.3))
                .frame(width: 24, height: 24)
                .overlay(ShimmerEffect())
            
            VStack(alignment: .leading, spacing: 6) {
                HStack {
                    SkeletonView(width: 140, height: 16)
                    Spacer()
                    SkeletonView(width: 40, height: 12)
                }
                
                SkeletonView(width: 200, height: 14)
                SkeletonView(width: 160, height: 14)
                
                SkeletonView(width: 80, height: 12)
            }
        }
        .padding(.vertical, 8)
    }
}

// MARK: - Country Detail Skeleton
struct CountryDetailSkeleton: View {
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // Hero section skeleton
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .frame(height: 200)
                    .overlay(ShimmerEffect())
                
                VStack(alignment: .leading, spacing: 16) {
                    // Stats section
                    VStack(alignment: .leading, spacing: 12) {
                        SkeletonView(width: 100, height: 18)
                        
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                            ForEach(0..<4, id: \.self) { _ in
                                VStack(alignment: .leading, spacing: 4) {
                                    SkeletonView(width: 60, height: 12)
                                    SkeletonView(width: 80, height: 14)
                                }
                                .padding(8)
                                .background(Color(.systemBackground))
                                .cornerRadius(8)
                            }
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    
                    // Safety overview
                    VStack(alignment: .leading, spacing: 12) {
                        SkeletonView(width: 120, height: 18)
                        SkeletonView(height: 14)
                        SkeletonView(width: 180, height: 14)
                        SkeletonView(width: 200, height: 14)
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    
                    // Health info
                    VStack(alignment: .leading, spacing: 12) {
                        SkeletonView(width: 140, height: 18)
                        
                        VStack(spacing: 8) {
                            ForEach(0..<4, id: \.self) { _ in
                                HStack {
                                    SkeletonView(width: 20, height: 16)
                                    SkeletonView(width: 100, height: 14)
                                    Spacer()
                                    SkeletonView(width: 60, height: 14)
                                }
                            }
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                }
                .padding(.horizontal)
            }
        }
    }
}

// MARK: - Map Skeleton
struct MapSkeleton: View {
    var body: some View {
        Rectangle()
            .fill(Color.gray.opacity(0.3))
            .overlay(
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        VStack(spacing: 8) {
                            ForEach(0..<3, id: \.self) { _ in
                                Circle()
                                    .fill(Color.gray.opacity(0.5))
                                    .frame(width: 20, height: 20)
                            }
                        }
                        .padding()
                        .background(Color.white.opacity(0.8))
                        .cornerRadius(8)
                        .padding()
                    }
                }
            )
            .overlay(ShimmerEffect())
    }
}

// MARK: - Search Results Skeleton
struct SearchResultsSkeleton: View {
    var body: some View {
        LazyVStack(spacing: 0) {
            ForEach(0..<8, id: \.self) { _ in
                CountryRowSkeleton()
                    .padding(.horizontal)
                Divider()
            }
        }
    }
}

// MARK: - Loading State View
struct LoadingStateView: View {
    let message: String
    let showProgress: Bool
    
    init(message: String = "Loading...", showProgress: Bool = true) {
        self.message = message
        self.showProgress = showProgress
    }
    
    var body: some View {
        VStack(spacing: 16) {
            if showProgress {
                ProgressView()
                    .scaleEffect(1.2)
            } else {
                LoadingDots()
            }
            
            Text(message)
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}

// MARK: - Empty State View
struct EmptyStateView: View {
    let icon: String
    let title: String
    let message: String
    let actionTitle: String?
    let action: (() -> Void)?
    
    init(
        icon: String,
        title: String,
        message: String,
        actionTitle: String? = nil,
        action: (() -> Void)? = nil
    ) {
        self.icon = icon
        self.title = title
        self.message = message
        self.actionTitle = actionTitle
        self.action = action
    }
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: icon)
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text(message)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }
            
            if let actionTitle = actionTitle, let action = action {
                Button(actionTitle, action: action)
                    .buttonStyle(.borderedProminent)
            }
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}

// MARK: - Error State View
struct ErrorStateView: View {
    let error: Error
    let retryAction: (() -> Void)?
    
    init(error: Error, retryAction: (() -> Void)? = nil) {
        self.error = error
        self.retryAction = retryAction
    }
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 48))
                .foregroundColor(.orange)
            
            VStack(spacing: 8) {
                Text("Something went wrong")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text(error.localizedDescription)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }
            
            if let retryAction = retryAction {
                Button("Try Again", action: retryAction)
                    .buttonStyle(.borderedProminent)
            }
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}

// MARK: - Skeleton Modifier
struct SkeletonModifier: ViewModifier {
    let isLoading: Bool
    
    func body(content: Content) -> some View {
        if isLoading {
            content
                .redacted(reason: .placeholder)
                .overlay(ShimmerEffect())
        } else {
            content
        }
    }
}

extension View {
    func skeleton(isLoading: Bool) -> some View {
        modifier(SkeletonModifier(isLoading: isLoading))
    }
}
