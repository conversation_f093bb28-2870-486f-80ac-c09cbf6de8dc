//
//  PerformanceDebugView.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import SwiftUI

/// Debug view for monitoring app performance
struct PerformanceDebugView: View {
    @StateObject private var imageCache = ImageCacheManager.shared
    @State private var performanceStats: [PerformanceStatistics] = []
    @State private var memoryUsage = MemoryUsage(used: 0, total: 0, percentage: 0)
    @State private var imageCacheInfo = CacheInfo(fileCount: 0, totalSize: 0, formattedSize: "0 B")
    @State private var refreshTimer: Timer?
    
    var body: some View {
        NavigationStack {
            List {
                // Memory Usage Section
                Section("Memory Usage") {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("Used Memory:")
                            Spacer()
                            Text(memoryUsage.formattedUsed)
                                .fontWeight(.medium)
                        }
                        
                        HStack {
                            Text("Total Memory:")
                            Spacer()
                            Text(memoryUsage.formattedTotal)
                                .fontWeight(.medium)
                        }
                        
                        HStack {
                            Text("Usage Percentage:")
                            Spacer()
                            Text("\(memoryUsage.percentage, specifier: "%.1f")%")
                                .fontWeight(.medium)
                                .foregroundColor(memoryUsage.percentage > 80 ? .red : .primary)
                        }
                        
                        ProgressView(value: memoryUsage.percentage, total: 100)
                            .progressViewStyle(LinearProgressViewStyle(tint: memoryUsage.percentage > 80 ? .red : .blue))
                    }
                }
                
                // Image Cache Section
                Section("Image Cache") {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("Cached Files:")
                            Spacer()
                            Text("\(imageCacheInfo.fileCount)")
                                .fontWeight(.medium)
                        }
                        
                        HStack {
                            Text("Cache Size:")
                            Spacer()
                            Text(imageCacheInfo.formattedSize)
                                .fontWeight(.medium)
                        }
                        
                        Button("Clear Image Cache") {
                            Task {
                                await imageCache.clearCache()
                                await updateCacheInfo()
                            }
                        }
                        .foregroundColor(.red)
                    }
                }
                
                // Performance Statistics Section
                Section("Performance Statistics") {
                    if performanceStats.isEmpty {
                        Text("No performance data available")
                            .foregroundColor(.secondary)
                    } else {
                        ForEach(performanceStats, id: \.operation) { stat in
                            VStack(alignment: .leading, spacing: 4) {
                                Text(stat.operation)
                                    .font(.headline)
                                
                                HStack {
                                    VStack(alignment: .leading) {
                                        Text("Average: \(stat.formattedAverage)")
                                        Text("Count: \(stat.count)")
                                    }
                                    .font(.caption)
                                    
                                    Spacer()
                                    
                                    VStack(alignment: .trailing) {
                                        Text("Min: \(stat.formattedMin)")
                                        Text("Max: \(stat.formattedMax)")
                                    }
                                    .font(.caption)
                                }
                                .foregroundColor(.secondary)
                            }
                            .padding(.vertical, 4)
                        }
                    }
                    
                    Button("Clear Performance Data") {
                        PerformanceMonitor.shared.clearMeasurements()
                        updatePerformanceStats()
                    }
                    .foregroundColor(.red)
                }
                
                // Settings Section
                Section("Debug Settings") {
                    Toggle("Performance Logging", isOn: .constant(UserDefaults.standard.performanceLoggingEnabled))
                        .onChange(of: UserDefaults.standard.performanceLoggingEnabled) { _, newValue in
                            UserDefaults.standard.performanceLoggingEnabled = newValue
                        }
                    
                    Toggle("Image Cache", isOn: .constant(UserDefaults.standard.imageCacheEnabled))
                        .onChange(of: UserDefaults.standard.imageCacheEnabled) { _, newValue in
                            UserDefaults.standard.imageCacheEnabled = newValue
                        }
                    
                    Button("Generate Performance Report") {
                        PerformanceMonitor.shared.logPerformanceSummary()
                        UserDefaults.standard.lastPerformanceReport = Date()
                    }
                    
                    if let lastReport = UserDefaults.standard.lastPerformanceReport {
                        Text("Last Report: \(lastReport.formatted(date: .abbreviated, time: .shortened))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                // Actions Section
                Section("Actions") {
                    Button("Force Memory Warning") {
                        PerformanceMonitor.shared.logMemoryWarning()
                    }
                    .foregroundColor(.orange)
                    
                    Button("Simulate Heavy Load") {
                        simulateHeavyLoad()
                    }
                    .foregroundColor(.orange)
                    
                    Button("Test Image Loading") {
                        testImageLoading()
                    }
                    .foregroundColor(.blue)
                }
            }
            .navigationTitle("Performance Debug")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Refresh") {
                        updateAllData()
                    }
                }
            }
            .onAppear {
                updateAllData()
                startAutoRefresh()
            }
            .onDisappear {
                stopAutoRefresh()
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func updateAllData() {
        updateMemoryUsage()
        updatePerformanceStats()
        Task {
            await updateCacheInfo()
        }
    }
    
    private func updateMemoryUsage() {
        memoryUsage = PerformanceMonitor.shared.getCurrentMemoryUsage()
    }
    
    private func updatePerformanceStats() {
        performanceStats = PerformanceMonitor.shared.getAllStatistics()
    }
    
    private func updateCacheInfo() async {
        imageCacheInfo = await imageCache.getCacheInfo()
    }
    
    private func startAutoRefresh() {
        refreshTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { _ in
            updateMemoryUsage()
        }
    }
    
    private func stopAutoRefresh() {
        refreshTimer?.invalidate()
        refreshTimer = nil
    }
    
    private func simulateHeavyLoad() {
        Task {
            let measurement = PerformanceMonitor.shared.startMeasurement("heavy_load_simulation")
            
            // Simulate CPU-intensive work
            for i in 0..<1000000 {
                _ = sqrt(Double(i))
            }
            
            measurement.end()
            updatePerformanceStats()
        }
    }
    
    private func testImageLoading() {
        Task {
            let testUrls = [
                "https://example.com/image1.jpg",
                "https://example.com/image2.jpg",
                "https://example.com/image3.jpg"
            ]
            
            let measurement = PerformanceMonitor.shared.startMeasurement("image_loading_test")
            
            await withTaskGroup(of: Void.self) { group in
                for url in testUrls {
                    group.addTask {
                        _ = await imageCache.loadImage(from: url)
                    }
                }
            }
            
            measurement.end()
            updatePerformanceStats()
            await updateCacheInfo()
        }
    }
}

// MARK: - Performance Overlay
struct PerformanceOverlay: View {
    @State private var memoryUsage = MemoryUsage(used: 0, total: 0, percentage: 0)
    @State private var showingDebugView = false
    @State private var refreshTimer: Timer?
    
    var body: some View {
        VStack {
            HStack {
                Spacer()
                
                Button(action: {
                    showingDebugView = true
                }) {
                    VStack(alignment: .trailing, spacing: 2) {
                        Text("MEM")
                            .font(.caption2)
                            .fontWeight(.bold)
                        
                        Text("\(memoryUsage.percentage, specifier: "%.0f")%")
                            .font(.caption2)
                            .fontWeight(.medium)
                    }
                    .padding(6)
                    .background(Color.black.opacity(0.7))
                    .foregroundColor(.white)
                    .cornerRadius(6)
                }
                .padding()
            }
            
            Spacer()
        }
        .sheet(isPresented: $showingDebugView) {
            PerformanceDebugView()
        }
        .onAppear {
            updateMemoryUsage()
            startAutoRefresh()
        }
        .onDisappear {
            stopAutoRefresh()
        }
    }
    
    private func updateMemoryUsage() {
        memoryUsage = PerformanceMonitor.shared.getCurrentMemoryUsage()
    }
    
    private func startAutoRefresh() {
        refreshTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            updateMemoryUsage()
        }
    }
    
    private func stopAutoRefresh() {
        refreshTimer?.invalidate()
        refreshTimer = nil
    }
}

#if DEBUG
// MARK: - Debug Preview
#Preview {
    PerformanceDebugView()
}
#endif
