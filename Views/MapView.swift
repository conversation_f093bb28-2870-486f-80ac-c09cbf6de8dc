//
//  MapView.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import SwiftUI
import MapKit

struct MapView: View {
    @EnvironmentObject var countryDataService: CountryDataService
    @State private var region = MKCoordinateRegion(
        center: CLLocationCoordinate2D(latitude: 20.0, longitude: 0.0),
        span: MKCoordinateSpan(latitudeDelta: 100.0, longitudeDelta: 100.0)
    )
    @State private var selectedCountry: Country?
    @State private var showingCountryDetail = false
    
    var body: some View {
        NavigationStack {
            ZStack {
                Map(coordinateRegion: $region, annotationItems: countryDataService.countries) { country in
                    MapAnnotation(coordinate: CLLocationCoordinate2D(latitude: country.latitude, longitude: country.longitude)) {
                        CountryMapPin(country: country) {
                            selectedCountry = country
                            showingCountryDetail = true
                        }
                    }
                }
                .ignoresSafeArea()
                
                VStack {
                    Spacer()
                    
                    if countryDataService.isLoading {
                        HStack {
                            ProgressView()
                                .scaleEffect(0.8)
                            Text("Loading countries...")
                                .font(.caption)
                        }
                        .padding()
                        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 12))
                        .padding()
                    }
                }
            }
            .navigationTitle("Safety Map")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        Task {
                            await countryDataService.refreshCountries()
                        }
                    } label: {
                        Image(systemName: "arrow.clockwise")
                    }
                    .disabled(countryDataService.isLoading)
                }
            }
        }
        .sheet(isPresented: $showingCountryDetail) {
            if let country = selectedCountry {
                CountryDetailView(country: country)
            }
        }
    }
}

struct CountryMapPin: View {
    let country: Country
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            ZStack {
                Circle()
                    .fill(safetyColor)
                    .frame(width: 20, height: 20)
                    .overlay(
                        Circle()
                            .stroke(Color.white, lineWidth: 2)
                    )
                
                Text(country.flagEmoji)
                    .font(.caption2)
            }
        }
        .scaleEffect(hasActiveAlerts ? 1.2 : 1.0)
        .animation(.easeInOut(duration: 0.3), value: hasActiveAlerts)
    }
    
    private var safetyColor: Color {
        switch country.overallSafetyRating {
        case .veryHigh:
            return .green
        case .high:
            return Color(red: 0.6, green: 0.8, blue: 0.2)
        case .moderate:
            return .yellow
        case .low:
            return .orange
        case .veryLow:
            return .red
        }
    }
    
    private var hasActiveAlerts: Bool {
        country.alerts.contains { $0.isActive && $0.severity == .critical }
    }
}

//#Preview {
//    MapView()
//        .environmentObject(CountryDataService(modelContext: ModelContext(try! ModelContainer(for: Country.self, Trip.self, TravelNotification.self, inMemory: true))))
//}
