//
//  NotificationsView.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import SwiftUI
import SwiftData

struct NotificationsView: View {
    @Environment(\.modelContext) private var modelContext
    @Query(sort: [SortDescriptor(\TravelNotification.createdDate, order: .reverse)]) 
    private var notifications: [TravelNotification]
    
    @State private var selectedFilter: NotificationFilter = .all
    @State private var showingMarkAllAsRead = false
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // Filter Picker
                Picker("Filter", selection: $selectedFilter) {
                    ForEach(NotificationFilter.allCases, id: \.self) { filter in
                        Text(filter.displayName).tag(filter)
                    }
                }
                .pickerStyle(.segmented)
                .padding()
                
                // Notifications List
                if filteredNotifications.isEmpty {
                    ContentUnavailableView(
                        "No Notifications",
                        systemImage: "bell.slash",
                        description: Text("You're all caught up! No \(selectedFilter.displayName.lowercased()) notifications at this time.")
                    )
                } else {
                    List {
                        ForEach(filteredNotifications) { notification in
                            NotificationRowView(notification: notification)
                                .swipeActions(edge: .trailing, allowsFullSwipe: true) {
                                    Button("Delete") {
                                        deleteNotification(notification)
                                    }
                                    .tint(.red)
                                }
                                .swipeActions(edge: .leading, allowsFullSwipe: true) {
                                    Button(notification.isRead ? "Mark Unread" : "Mark Read") {
                                        toggleReadStatus(notification)
                                    }
                                    .tint(notification.isRead ? .orange : .blue)
                                }
                        }
                    }
                    .listStyle(.plain)
                }
            }
            .navigationTitle("Notifications")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Mark All as Read") {
                            markAllAsRead()
                        }
                        .disabled(unreadNotifications.isEmpty)
                        
                        Button("Clear All Read") {
                            clearReadNotifications()
                        }
                        .disabled(readNotifications.isEmpty)
                        
                        Divider()
                        
                        Button("Add Test Notification") {
                            addTestNotification()
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
                
                ToolbarItem(placement: .navigationBarLeading) {
                    if !unreadNotifications.isEmpty {
                        Text("\(unreadNotifications.count) unread")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var filteredNotifications: [TravelNotification] {
        switch selectedFilter {
        case .all:
            return notifications.filter { $0.shouldDisplay }
        case .unread:
            return notifications.filter { !$0.isRead && $0.shouldDisplay }
        case .alerts:
            return notifications.filter { 
                ($0.type == .safetyAlert || $0.priority == .critical) && $0.shouldDisplay 
            }
        }
    }
    
    private var unreadNotifications: [TravelNotification] {
        notifications.filter { !$0.isRead && $0.shouldDisplay }
    }
    
    private var readNotifications: [TravelNotification] {
        notifications.filter { $0.isRead }
    }
    
    // MARK: - Actions
    
    private func toggleReadStatus(_ notification: TravelNotification) {
        notification.isRead.toggle()
        try? modelContext.save()
    }
    
    private func deleteNotification(_ notification: TravelNotification) {
        modelContext.delete(notification)
        try? modelContext.save()
    }
    
    private func markAllAsRead() {
        for notification in unreadNotifications {
            notification.markAsRead()
        }
        try? modelContext.save()
    }
    
    private func clearReadNotifications() {
        for notification in readNotifications {
            modelContext.delete(notification)
        }
        try? modelContext.save()
    }
    
    private func addTestNotification() {
        let testNotification = TravelNotification(
            title: "Test Safety Alert",
            message: "This is a test notification to demonstrate the notification system.",
            type: .safetyAlert,
            priority: .medium,
            countryName: "Test Country"
        )
        
        modelContext.insert(testNotification)
        try? modelContext.save()
    }
}

// MARK: - Notification Filter Enum
enum NotificationFilter: String, CaseIterable {
    case all = "all"
    case unread = "unread"
    case alerts = "alerts"
    
    var displayName: String {
        switch self {
        case .all: return "All"
        case .unread: return "Unread"
        case .alerts: return "Alerts"
        }
    }
}

// MARK: - Notification Row View
struct NotificationRowView: View {
    let notification: TravelNotification
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // Icon
            Image(systemName: notification.iconName)
                .foregroundColor(Color(notification.priorityColor))
                .font(.title2)
                .frame(width: 24, height: 24)
            
            // Content
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(notification.title)
                        .font(.headline)
                        .fontWeight(notification.isRead ? .medium : .semibold)
                    
                    Spacer()
                    
                    Text(notification.timeAgoText)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Text(notification.message)
                    .font(.body)
                    .foregroundColor(notification.isRead ? .secondary : .primary)
                    .lineLimit(3)
                
                if let countryName = notification.countryName {
                    HStack {
                        Image(systemName: "location.fill")
                            .font(.caption)
                        Text(countryName)
                            .font(.caption)
                    }
                    .foregroundColor(.secondary)
                }
            }
            
            // Unread indicator
            if !notification.isRead {
                Circle()
                    .fill(.blue)
                    .frame(width: 8, height: 8)
            }
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    NotificationsView()
        .modelContainer(for: [TravelNotification.self], inMemory: true)
}
