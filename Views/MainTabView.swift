//
//  MainTabView.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import SwiftUI
import SwiftData

struct MainTabView: View {
    @Environment(\.modelContext) private var modelContext
    @State private var countryDataService: CountryDataService?
    
    var body: some View {
        Group {
            if let service = countryDataService {
                TabView {
                    MapView()
                        .tabItem {
                            Image(systemName: "map.fill")
                            Text("Map")
                        }
                        .tag(0)

                    NotificationsView()
                        .tabItem {
                            Image(systemName: "bell.fill")
                            Text("Notifications")
                        }
                        .tag(1)

                    TripsView()
                        .tabItem {
                            Image(systemName: "airplane.departure")
                            Text("Trips")
                        }
                        .tag(2)

                    SearchView()
                        .tabItem {
                            Image(systemName: "magnifyingglass")
                            Text("Search")
                        }
                        .tag(3)
                }
                .environmentObject(service)
            } else {
                ProgressView("Loading...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .onAppear {
            if countryDataService == nil {
                let service = CountryDataService(modelContext: modelContext)
                countryDataService = service
                Task {
                    await service.loadCountries()
                }
            }
        }
    }
}

#Preview {
    MainTabView()
        .modelContainer(for: [Country.self, Trip.self, TravelNotification.self], inMemory: true)
}
