//
//  TripDetailView.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import SwiftUI
import SwiftData

struct TripDetailView: View {
    @Environment(\.modelContext) private var modelContext
    @EnvironmentObject var countryDataService: CountryDataService
    @Bindable var trip: Trip
    @State private var showingEditTrip = false
    @State private var showingDeleteAlert = false
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // Trip Header
                TripHeaderView(trip: trip)
                
                VStack(alignment: .leading, spacing: 16) {
                    // Trip Details
                    TripDetailsCard(trip: trip)
                    
                    // Country Information
                    if let country = countryDataService.getCountry(by: trip.countryId) {
                        CountryInformationCard(country: country)
                        
                        // Safety Alerts for this country
                        if !country.alerts.filter({ $0.isActive }).isEmpty {
                            SafetyAlertsCard(alerts: country.alerts.filter { $0.isActive })
                        }
                        
                        // Quick Safety Info
                        QuickSafetyInfoCard(country: country)
                    }
                    
                    // Trip Notes
                    if !trip.notes.isEmpty {
                        TripNotesCard(notes: trip.notes)
                    }
                }
                .padding(.horizontal)
            }
        }
        .navigationTitle(trip.countryName)
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Menu {
                    Button("Edit Trip") {
                        showingEditTrip = true
                    }
                    
                    Divider()
                    
                    Button("Delete Trip", role: .destructive) {
                        showingDeleteAlert = true
                    }
                } label: {
                    Image(systemName: "ellipsis.circle")
                }
            }
        }
        .sheet(isPresented: $showingEditTrip) {
            EditTripView(trip: trip)
        }
        .alert("Delete Trip", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                deleteTrip()
            }
        } message: {
            Text("Are you sure you want to delete this trip? This action cannot be undone.")
        }
    }
    
    private func deleteTrip() {
        modelContext.delete(trip)
        try? modelContext.save()
    }
}

// MARK: - Trip Header View
struct TripHeaderView: View {
    let trip: Trip
    
    var body: some View {
        ZStack(alignment: .bottomLeading) {
            // Background
            Rectangle()
                .fill(LinearGradient(
                    colors: [Color.accentColor.opacity(0.6), Color.accentColor.opacity(0.8)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
                .frame(height: 180)
            
            // Trip Info
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text(trip.countryFlag)
                        .font(.system(size: 50))
                    
                    VStack(alignment: .leading) {
                        Text(trip.countryName)
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        HStack {
                            Image(systemName: trip.status.iconName)
                            Text(trip.status.displayName)
                        }
                        .font(.headline)
                        .foregroundColor(.white.opacity(0.9))
                    }
                    
                    Spacer()
                }
                
                Text(trip.formattedDateRange)
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.9))
                
                Text(trip.statusText)
                    .font(.caption)
                    .fontWeight(.medium)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(.white.opacity(0.2))
                    .foregroundColor(.white)
                    .cornerRadius(8)
            }
            .padding()
        }
    }
}

// MARK: - Trip Details Card
struct TripDetailsCard: View {
    let trip: Trip
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Trip Details")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                DetailRow(title: "Purpose", value: trip.purpose.displayName, icon: trip.purpose.iconName)
                DetailRow(title: "Duration", value: trip.durationText, icon: "calendar")
                DetailRow(title: "Created", value: formatDate(trip.createdDate), icon: "clock")
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: date)
    }
}

// MARK: - Country Information Card
struct CountryInformationCard: View {
    let country: Country
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Country Information")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                DetailRow(title: "Safety Rating", value: country.overallSafetyRating.displayName, icon: "shield")
                DetailRow(title: "Capital", value: country.capital, icon: "building.2")
                DetailRow(title: "Currency", value: country.currency, icon: "dollarsign.circle")
                DetailRow(title: "Emergency Number", value: country.healthInfo.emergencyNumber, icon: "phone.fill")
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Safety Alerts Card
struct SafetyAlertsCard: View {
    let alerts: [SafetyAlert]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.orange)
                Text("Active Safety Alerts")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            ForEach(alerts.prefix(3)) { alert in
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(alert.title)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                        
                        Spacer()
                        
                        Text(alert.severity.displayName)
                            .font(.caption)
                            .fontWeight(.medium)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color(alert.severity.color).opacity(0.2))
                            .foregroundColor(Color(alert.severity.color))
                            .cornerRadius(4)
                    }
                    
                    Text(alert.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                .padding(8)
                .background(Color(.systemBackground))
                .cornerRadius(8)
            }
            
            if alerts.count > 3 {
                Text("+ \(alerts.count - 3) more alerts")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Quick Safety Info Card
struct QuickSafetyInfoCard: View {
    let country: Country
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quick Safety Info")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                DetailRow(title: "Crime Rate", value: country.safetyInfo.crimeRate.displayName, icon: "shield.slash")
                DetailRow(title: "Healthcare Quality", value: country.healthInfo.healthcareQuality.displayName, icon: "cross.case")
                DetailRow(title: "Water Safety", value: country.healthInfo.waterSafety.displayName, icon: country.healthInfo.waterSafety.iconName)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Trip Notes Card
struct TripNotesCard: View {
    let notes: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Notes")
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(notes)
                .font(.body)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Detail Row View
struct DetailRow: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.accentColor)
                .frame(width: 20)
            
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
        }
    }
}

// MARK: - Edit Trip View
struct EditTripView: View {
    @Environment(\.dismiss) private var dismiss
    @Bindable var trip: Trip
    
    @State private var startDate: Date
    @State private var endDate: Date
    @State private var purpose: TripPurpose
    @State private var notes: String
    
    init(trip: Trip) {
        self.trip = trip
        self._startDate = State(initialValue: trip.startDate)
        self._endDate = State(initialValue: trip.endDate)
        self._purpose = State(initialValue: trip.purpose)
        self._notes = State(initialValue: trip.notes)
    }
    
    var body: some View {
        NavigationStack {
            Form {
                Section("Travel Dates") {
                    DatePicker("Start Date", selection: $startDate, displayedComponents: .date)
                    DatePicker("End Date", selection: $endDate, displayedComponents: .date)
                }
                
                Section("Trip Details") {
                    Picker("Purpose", selection: $purpose) {
                        ForEach(TripPurpose.allCases, id: \.self) { purpose in
                            HStack {
                                Image(systemName: purpose.iconName)
                                Text(purpose.displayName)
                            }
                            .tag(purpose)
                        }
                    }
                    
                    TextField("Notes", text: $notes, axis: .vertical)
                        .lineLimit(3...6)
                }
            }
            .navigationTitle("Edit Trip")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveChanges()
                    }
                    .fontWeight(.semibold)
                    .disabled(startDate >= endDate)
                }
            }
        }
    }
    
    private func saveChanges() {
        trip.updateTrip(
            startDate: startDate,
            endDate: endDate,
            purpose: purpose,
            notes: notes
        )
        dismiss()
    }
}

//#Preview {
//    let trip = Trip(
//        countryId: "japan",
//        countryName: "Japan",
//        countryFlag: "🇯🇵",
//        startDate: Date(),
//        endDate: Calendar.current.date(byAdding: .day, value: 7, to: Date()) ?? Date(),
//        purpose: .leisure,
//        notes: "Visiting Tokyo and Kyoto"
//    )
//    
//    return TripDetailView(trip: trip)
//        .environmentObject(CountryDataService(modelContext: ModelContext(try! ModelContainer(for: Country.self, Trip.self, TravelNotification.self, inMemory: true))))
//}
