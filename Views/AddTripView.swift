//
//  AddTripView.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import SwiftUI
import SwiftData

struct AddTripView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var countryDataService: CountryDataService
    
    let preselectedCountry: Country?
    
    @State private var selectedCountry: Country?
    @State private var startDate = Date()
    @State private var endDate = Calendar.current.date(byAdding: .day, value: 7, to: Date()) ?? Date()
    @State private var purpose: TripPurpose = .leisure
    @State private var notes = ""
    @State private var showingCountryPicker = false
    @State private var showingValidationAlert = false
    @State private var validationMessage = ""
    
    init(preselectedCountry: Country? = nil) {
        self.preselectedCountry = preselectedCountry
    }
    
    var body: some View {
        NavigationStack {
            Form {
                Section("Destination") {
                    if let country = selectedCountry {
                        HStack {
                            Text(country.flagEmoji)
                                .font(.title2)
                            
                            VStack(alignment: .leading) {
                                Text(country.name)
                                    .font(.headline)
                                Text(country.continent)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            Button("Change") {
                                showingCountryPicker = true
                            }
                            .font(.caption)
                        }
                    } else {
                        Button("Select Country") {
                            showingCountryPicker = true
                        }
                    }
                }
                
                Section("Travel Dates") {
                    DatePicker("Start Date", selection: $startDate, displayedComponents: .date)
                    DatePicker("End Date", selection: $endDate, displayedComponents: .date)
                    
                    if startDate < endDate {
                        HStack {
                            Text("Duration")
                                .foregroundColor(.secondary)
                            Spacer()
                            Text("\(daysBetween(startDate, endDate)) days")
                                .fontWeight(.medium)
                        }
                    }
                }
                
                Section("Trip Details") {
                    Picker("Purpose", selection: $purpose) {
                        ForEach(TripPurpose.allCases, id: \.self) { purpose in
                            HStack {
                                Image(systemName: purpose.iconName)
                                Text(purpose.displayName)
                            }
                            .tag(purpose)
                        }
                    }
                    
                    TextField("Notes (optional)", text: $notes, axis: .vertical)
                        .lineLimit(3...6)
                }
                
                if let country = selectedCountry {
                    Section("Safety Information") {
                        HStack {
                            Text("Safety Rating")
                            Spacer()
                            SafetyBadge(rating: country.overallSafetyRating)
                        }
                        
                        if country.alerts.contains(where: { $0.isActive }) {
                            HStack {
                                Image(systemName: "exclamationmark.triangle.fill")
                                    .foregroundColor(.orange)
                                Text("This country has active safety alerts")
                                    .font(.caption)
                                    .foregroundColor(.orange)
                            }
                        }
                    }
                }
            }
            .navigationTitle("Add Trip")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveTrip()
                    }
                    .fontWeight(.semibold)
                    .disabled(!isFormValid)
                }
            }
        }
        .sheet(isPresented: $showingCountryPicker) {
            CountryPickerView(selectedCountry: $selectedCountry)
        }
        .alert("Invalid Trip", isPresented: $showingValidationAlert) {
            Button("OK") { }
        } message: {
            Text(validationMessage)
        }
        .onAppear {
            if let preselected = preselectedCountry {
                selectedCountry = preselected
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var isFormValid: Bool {
        selectedCountry != nil && startDate < endDate
    }
    
    // MARK: - Methods
    
    private func daysBetween(_ start: Date, _ end: Date) -> Int {
        Calendar.current.dateComponents([.day], from: start, to: end).day ?? 0
    }
    
    private func saveTrip() {
        guard let country = selectedCountry else {
            validationMessage = "Please select a country"
            showingValidationAlert = true
            return
        }
        
        guard startDate < endDate else {
            validationMessage = "End date must be after start date"
            showingValidationAlert = true
            return
        }
        
        let trip = Trip(
            countryId: country.id,
            countryName: country.name,
            countryFlag: country.flagEmoji,
            startDate: startDate,
            endDate: endDate,
            purpose: purpose,
            notes: notes
        )
        
        modelContext.insert(trip)
        
        do {
            try modelContext.save()
            dismiss()
        } catch {
            validationMessage = "Failed to save trip: \(error.localizedDescription)"
            showingValidationAlert = true
        }
    }
}

// MARK: - Country Picker View
struct CountryPickerView: View {
    @Binding var selectedCountry: Country?
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var countryDataService: CountryDataService
    @State private var searchText = ""
    
    var body: some View {
        NavigationStack {
            List {
                ForEach(filteredCountries) { country in
                    Button {
                        selectedCountry = country
                        dismiss()
                    } label: {
                        HStack {
                            Text(country.flagEmoji)
                                .font(.title2)
                            
                            VStack(alignment: .leading) {
                                Text(country.name)
                                    .font(.headline)
                                    .foregroundColor(.primary)
                                Text(country.continent)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            SafetyBadge(rating: country.overallSafetyRating)
                        }
                    }
                }
            }
            .searchable(text: $searchText, prompt: "Search countries")
            .navigationTitle("Select Country")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var filteredCountries: [Country] {
        if searchText.isEmpty {
            return countryDataService.countries.sorted { $0.name < $1.name }
        } else {
            return countryDataService.searchCountries(query: searchText)
        }
    }
}

//#Preview {
//    AddTripView()
//        .environmentObject(CountryDataService(modelContext: ModelContext(try! ModelContainer(for: Country.self, Trip.self, TravelNotification.self, inMemory: true))))
//        .modelContainer(for: [Trip.self], inMemory: true)
//}
