//
//  CountryDetailView.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import SwiftUI

struct CountryDetailView: View {
    let country: Country
    @Environment(\.dismiss) private var dismiss
    @State private var showingAddTrip = false
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Hero Section
                    CountryHeroView(country: country)
                    
                    VStack(alignment: .leading, spacing: 16) {
                        // Quick Stats
                        CountryStatsView(country: country)
                        
                        // Safety Overview
                        SafetyOverviewView(country: country)
                        
                        // Active Alerts
                        if !country.alerts.filter({ $0.isActive }).isEmpty {
                            ActiveAlertsView(alerts: country.alerts.filter { $0.isActive })
                        }
                        
                        // Health Information
                        HealthInformationView(healthInfo: country.healthInfo)
                        
                        // Safety Information
                        SafetyInformationView(safetyInfo: country.safetyInfo)
                        
                        // Travel Requirements
                        TravelRequirementsView(country: country)
                    }
                    .padding(.horizontal)
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Add Trip") {
                        showingAddTrip = true
                    }
                    .buttonStyle(.borderedProminent)
                }
            }
        }
        .sheet(isPresented: $showingAddTrip) {
            AddTripView(preselectedCountry: country)
        }
    }
}

// MARK: - Country Hero View
struct CountryHeroView: View {
    let country: Country
    
    var body: some View {
        ZStack(alignment: .bottomLeading) {
            // Background Image (placeholder)
            Rectangle()
                .fill(LinearGradient(
                    colors: [.blue.opacity(0.6), .purple.opacity(0.8)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
                .frame(height: 200)
            
            // Country Info Overlay
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text(country.flagEmoji)
                        .font(.system(size: 40))
                    
                    VStack(alignment: .leading) {
                        Text(country.name)
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        Text(country.continent)
                            .font(.headline)
                            .foregroundColor(.white.opacity(0.9))
                    }
                    
                    Spacer()
                }
                
                SafetyBadge(rating: country.overallSafetyRating)
            }
            .padding()
        }
    }
}

// MARK: - Country Stats View
struct CountryStatsView: View {
    let country: Country
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quick Facts")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                StatCard(title: "Capital", value: country.capital, icon: "building.2")
                StatCard(title: "Population", value: formatPopulation(country.population), icon: "person.3")
                StatCard(title: "Currency", value: country.currency, icon: "dollarsign.circle")
                StatCard(title: "Language", value: country.language, icon: "bubble.left")
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private func formatPopulation(_ population: Int) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        return formatter.string(from: NSNumber(value: population)) ?? "\(population)"
    }
}

// MARK: - Stat Card View
struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.accentColor)
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
            }
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
                .lineLimit(2)
        }
        .padding(8)
        .background(Color(.systemBackground))
        .cornerRadius(8)
    }
}

// MARK: - Safety Overview View
struct SafetyOverviewView: View {
    let country: Country
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Safety Overview")
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(country.descriptions)
                .font(.body)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Active Alerts View
struct ActiveAlertsView: View {
    let alerts: [SafetyAlert]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Active Alerts")
                .font(.headline)
                .fontWeight(.semibold)
            
            ForEach(alerts.prefix(3)) { alert in
                AlertCard(alert: alert)
            }
            
            if alerts.count > 3 {
                Text("+ \(alerts.count - 3) more alerts")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Alert Card View
struct AlertCard: View {
    let alert: SafetyAlert
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: alert.iconName)
                .foregroundColor(Color(alert.severity.color))
                .font(.title2)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(alert.title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(alert.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
        }
        .padding(8)
        .background(Color(.systemBackground))
        .cornerRadius(8)
    }
}

// MARK: - Health Information View
struct HealthInformationView: View {
    let healthInfo: HealthInformation
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Health Information")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                InfoRow(title: "Healthcare Quality", value: healthInfo.healthcareQuality.displayName, icon: "cross.case")
                InfoRow(title: "Water Safety", value: healthInfo.waterSafety.displayName, icon: healthInfo.waterSafety.iconName)
                InfoRow(title: "Food Safety", value: healthInfo.foodSafety.displayName, icon: "fork.knife")
                InfoRow(title: "Emergency Number", value: healthInfo.emergencyNumber, icon: "phone")
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Safety Information View
struct SafetyInformationView: View {
    let safetyInfo: SafetyInformation
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Safety Information")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                InfoRow(title: "Crime Rate", value: safetyInfo.crimeRate.displayName, icon: "shield")
                InfoRow(title: "Terrorism Threat", value: safetyInfo.terrorismThreat.displayName, icon: "exclamationmark.triangle")
                InfoRow(title: "Political Stability", value: safetyInfo.politicalStability.displayName, icon: "building.columns")
                InfoRow(title: "Road Safety", value: safetyInfo.roadSafety.displayName, icon: "car")
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Travel Requirements View
struct TravelRequirementsView: View {
    let country: Country
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Travel Requirements")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                InfoRow(
                    title: "Visa Required",
                    value: country.visaRequired ? "Yes" : "No",
                    icon: "doc.text"
                )
                
                if !country.vaccinationsRequired.isEmpty {
                    InfoRow(
                        title: "Required Vaccinations",
                        value: country.vaccinationsRequired.joined(separator: ", "),
                        icon: "syringe"
                    )
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Info Row View
struct InfoRow: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.accentColor)
                .frame(width: 20)
            
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
        }
    }
}

#Preview {
    CountryDetailView(country: Country(
        id: "japan",
        name: "Japan",
        flagEmoji: "🇯🇵",
        description: "Japan is known for its safety and rich culture.",
        photoURL: "",
        continent: "Asia",
        capital: "Tokyo",
        population: 125800000,
        currency: "Japanese Yen",
        language: "Japanese",
        overallSafetyRating: .veryHigh,
        healthInfo: HealthInformation(
            healthcareQuality: .excellent,
            waterSafety: .safe,
            foodSafety: .safe,
            airQuality: .good,
            emergencyNumber: "119",
            healthInsuranceRequired: false
        ),
        safetyInfo: SafetyInformation(
            crimeRate: .veryLow,
            terrorismThreat: .low,
            politicalStability: .veryStable,
            naturalDisasterRisk: .medium,
            roadSafety: .excellent,
            publicTransportSafety: .excellent,
            emergencyServices: EmergencyServices(
                police: "110",
                fire: "119",
                medical: "119",
                general: "110",
                responseTime: .fast,
                reliability: .excellent
            )
        ),
        latitude: 36.2048,
        longitude: 138.2529,
        timeZone: "Asia/Tokyo",
        visaRequired: false
    ))
}
