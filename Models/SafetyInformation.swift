//
//  SafetyInformation.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import Foundation

/// Represents comprehensive safety information for a country
struct SafetyInformation: Codable, Hashable {
    let crimeRate: CrimeRate
    let terrorismThreat: ThreatLevel
    let politicalStability: StabilityLevel
    let naturalDisasterRisk: RiskLevel
    let roadSafety: SafetyLevel
    let publicTransportSafety: SafetyLevel
    let emergencyServices: EmergencyServices
    let safeAreas: [SafeArea]
    let areasToAvoid: [DangerousArea]
    let generalSafetyTips: [String]
    let emergencyContacts: [EmergencyContact]
    let localLaws: [LocalLaw]
    
    init(
        crimeRate: CrimeRate,
        terrorismThreat: ThreatLevel,
        politicalStability: StabilityLevel,
        naturalDisasterRisk: RiskLevel,
        roadSafety: SafetyLevel,
        publicTransportSafety: SafetyLevel,
        emergencyServices: EmergencyServices,
        safeAreas: [SafeArea] = [],
        areasToAvoid: [DangerousArea] = [],
        generalSafetyTips: [String] = [],
        emergencyContacts: [EmergencyContact] = [],
        localLaws: [LocalLaw] = []
    ) {
        self.crimeRate = crimeRate
        self.terrorismThreat = terrorismThreat
        self.politicalStability = politicalStability
        self.naturalDisasterRisk = naturalDisasterRisk
        self.roadSafety = roadSafety
        self.publicTransportSafety = publicTransportSafety
        self.emergencyServices = emergencyServices
        self.safeAreas = safeAreas
        self.areasToAvoid = areasToAvoid
        self.generalSafetyTips = generalSafetyTips
        self.emergencyContacts = emergencyContacts
        self.localLaws = localLaws
    }
}

// MARK: - Crime Rate Enum
enum CrimeRate: String, Codable, CaseIterable {
    case veryLow = "very_low"
    case low = "low"
    case moderate = "moderate"
    case high = "high"
    case veryHigh = "very_high"
    
    var displayName: String {
        switch self {
        case .veryLow: return "Very Low"
        case .low: return "Low"
        case .moderate: return "Moderate"
        case .high: return "High"
        case .veryHigh: return "Very High"
        }
    }
    
    var color: String {
        switch self {
        case .veryLow: return "green"
        case .low: return "lightGreen"
        case .moderate: return "yellow"
        case .high: return "orange"
        case .veryHigh: return "red"
        }
    }
}

// MARK: - Threat Level Enum
enum ThreatLevel: String, Codable, CaseIterable {
    case minimal = "minimal"
    case low = "low"
    case moderate = "moderate"
    case high = "high"
    case severe = "severe"
    
    var displayName: String {
        switch self {
        case .minimal: return "Minimal"
        case .low: return "Low"
        case .moderate: return "Moderate"
        case .high: return "High"
        case .severe: return "Severe"
        }
    }
    
    var color: String {
        switch self {
        case .minimal: return "green"
        case .low: return "lightGreen"
        case .moderate: return "yellow"
        case .high: return "orange"
        case .severe: return "red"
        }
    }
}

// MARK: - Stability Level Enum
enum StabilityLevel: String, Codable, CaseIterable {
    case veryStable = "very_stable"
    case stable = "stable"
    case moderate = "moderate"
    case unstable = "unstable"
    case veryUnstable = "very_unstable"
    
    var displayName: String {
        switch self {
        case .veryStable: return "Very Stable"
        case .stable: return "Stable"
        case .moderate: return "Moderate"
        case .unstable: return "Unstable"
        case .veryUnstable: return "Very Unstable"
        }
    }
    
    var color: String {
        switch self {
        case .veryStable: return "green"
        case .stable: return "lightGreen"
        case .moderate: return "yellow"
        case .unstable: return "orange"
        case .veryUnstable: return "red"
        }
    }
}

// MARK: - Safety Level Enum
enum SafetyLevel: String, Codable, CaseIterable {
    case excellent = "excellent"
    case good = "good"
    case fair = "fair"
    case poor = "poor"
    case dangerous = "dangerous"
    
    var displayName: String {
        switch self {
        case .excellent: return "Excellent"
        case .good: return "Good"
        case .fair: return "Fair"
        case .poor: return "Poor"
        case .dangerous: return "Dangerous"
        }
    }
    
    var color: String {
        switch self {
        case .excellent: return "green"
        case .good: return "lightGreen"
        case .fair: return "yellow"
        case .poor: return "orange"
        case .dangerous: return "red"
        }
    }
}

// MARK: - Emergency Services Model
struct EmergencyServices: Codable, Hashable {
    let police: String
    let fire: String
    let medical: String
    let general: String
    let responseTime: ResponseTime
    let reliability: ServiceReliability
    
    init(
        police: String,
        fire: String,
        medical: String,
        general: String,
        responseTime: ResponseTime,
        reliability: ServiceReliability
    ) {
        self.police = police
        self.fire = fire
        self.medical = medical
        self.general = general
        self.responseTime = responseTime
        self.reliability = reliability
    }
}

// MARK: - Response Time Enum
enum ResponseTime: String, Codable, CaseIterable {
    case immediate = "immediate"
    case fast = "fast"
    case moderate = "moderate"
    case slow = "slow"
    case unreliable = "unreliable"
    
    var displayName: String {
        switch self {
        case .immediate: return "Immediate (< 5 min)"
        case .fast: return "Fast (5-15 min)"
        case .moderate: return "Moderate (15-30 min)"
        case .slow: return "Slow (30+ min)"
        case .unreliable: return "Unreliable"
        }
    }
}

// MARK: - Service Reliability Enum
enum ServiceReliability: String, Codable, CaseIterable {
    case excellent = "excellent"
    case good = "good"
    case fair = "fair"
    case poor = "poor"
    
    var displayName: String {
        switch self {
        case .excellent: return "Excellent"
        case .good: return "Good"
        case .fair: return "Fair"
        case .poor: return "Poor"
        }
    }
}

// MARK: - Safe Area Model
struct SafeArea: Identifiable, Codable, Hashable {
    let id: String
    let name: String
    let description: String
    let safetyFeatures: [String]
    
    init(
        id: String = UUID().uuidString,
        name: String,
        description: String,
        safetyFeatures: [String] = []
    ) {
        self.id = id
        self.name = name
        self.description = description
        self.safetyFeatures = safetyFeatures
    }
}

// MARK: - Dangerous Area Model
struct DangerousArea: Identifiable, Codable, Hashable {
    let id: String
    let name: String
    let description: String
    let riskFactors: [String]
    let avoidanceRecommendations: [String]
    
    init(
        id: String = UUID().uuidString,
        name: String,
        description: String,
        riskFactors: [String] = [],
        avoidanceRecommendations: [String] = []
    ) {
        self.id = id
        self.name = name
        self.description = description
        self.riskFactors = riskFactors
        self.avoidanceRecommendations = avoidanceRecommendations
    }
}

// MARK: - Emergency Contact Model
struct EmergencyContact: Identifiable, Codable, Hashable {
    let id: String
    let name: String
    let phoneNumber: String
    let type: ContactType
    let description: String
    
    init(
        id: String = UUID().uuidString,
        name: String,
        phoneNumber: String,
        type: ContactType,
        description: String
    ) {
        self.id = id
        self.name = name
        self.phoneNumber = phoneNumber
        self.type = type
        self.description = description
    }
}

// MARK: - Contact Type Enum
enum ContactType: String, Codable, CaseIterable {
    case embassy = "embassy"
    case consulate = "consulate"
    case police = "police"
    case medical = "medical"
    case fire = "fire"
    case tourist = "tourist"
    case other = "other"
    
    var displayName: String {
        switch self {
        case .embassy: return "Embassy"
        case .consulate: return "Consulate"
        case .police: return "Police"
        case .medical: return "Medical"
        case .fire: return "Fire Department"
        case .tourist: return "Tourist Information"
        case .other: return "Other"
        }
    }
}

// MARK: - Local Law Model
struct LocalLaw: Identifiable, Codable, Hashable {
    let id: String
    let title: String
    let description: String
    let penalty: String
    let importance: LawImportance
    
    init(
        id: String = UUID().uuidString,
        title: String,
        description: String,
        penalty: String,
        importance: LawImportance
    ) {
        self.id = id
        self.title = title
        self.description = description
        self.penalty = penalty
        self.importance = importance
    }
}

// MARK: - Law Importance Enum
enum LawImportance: String, Codable, CaseIterable {
    case critical = "critical"
    case important = "important"
    case moderate = "moderate"
    case minor = "minor"
    
    var displayName: String {
        switch self {
        case .critical: return "Critical"
        case .important: return "Important"
        case .moderate: return "Moderate"
        case .minor: return "Minor"
        }
    }
    
    var color: String {
        switch self {
        case .critical: return "red"
        case .important: return "orange"
        case .moderate: return "yellow"
        case .minor: return "blue"
        }
    }
}
