//
//  TravelNotification.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import Foundation
import SwiftData

/// Represents a travel-related notification for the user
@Model
final class TravelNotification: Identifiable {
    @Attribute(.unique) var id: String
    var title: String
    var message: String
    var type: NotificationType
    var priority: NotificationPriority
    var countryId: String?
    var countryName: String?
    var tripId: String?
    var isRead: Bool
    var createdDate: Date
    var scheduledDate: Date?
    var expiryDate: Date?
    var actionURL: String?
    var metadata: [String: String]
    
    init(
        id: String = UUID().uuidString,
        title: String,
        message: String,
        type: NotificationType,
        priority: NotificationPriority,
        countryId: String? = nil,
        countryName: String? = nil,
        tripId: String? = nil,
        isRead: Bool = false,
        createdDate: Date = Date(),
        scheduledDate: Date? = nil,
        expiryDate: Date? = nil,
        actionURL: String? = nil,
        metadata: [String: String] = [:]
    ) {
        self.id = id
        self.title = title
        self.message = message
        self.type = type
        self.priority = priority
        self.countryId = countryId
        self.countryName = countryName
        self.tripId = tripId
        self.isRead = isRead
        self.createdDate = createdDate
        self.scheduledDate = scheduledDate
        self.expiryDate = expiryDate
        self.actionURL = actionURL
        self.metadata = metadata
    }
    
    var isExpired: Bool {
        guard let expiryDate = expiryDate else { return false }
        return Date() > expiryDate
    }
    
    var isScheduled: Bool {
        guard let scheduledDate = scheduledDate else { return false }
        return Date() < scheduledDate
    }
    
    var shouldDisplay: Bool {
        return !isExpired && !isScheduled
    }
    
    var iconName: String {
        switch type {
        case .safetyAlert: return "exclamationmark.triangle.fill"
        case .healthAdvisory: return "cross.case.fill"
        case .travelUpdate: return "airplane.departure"
        case .tripReminder: return "clock.fill"
        case .documentExpiry: return "doc.text.fill"
        case .weatherAlert: return "cloud.bolt.rain.fill"
        case .general: return "bell.fill"
        case .system: return "gear.circle.fill"
        }
    }
    
    var priorityColor: String {
        switch priority {
        case .critical: return "red"
        case .high: return "orange"
        case .medium: return "yellow"
        case .low: return "blue"
        case .info: return "gray"
        }
    }
    
    func markAsRead() {
        isRead = true
    }
    
    func markAsUnread() {
        isRead = false
    }
}

// MARK: - Notification Type Enum
enum NotificationType: String, Codable, CaseIterable {
    case safetyAlert = "safety_alert"
    case healthAdvisory = "health_advisory"
    case travelUpdate = "travel_update"
    case tripReminder = "trip_reminder"
    case documentExpiry = "document_expiry"
    case weatherAlert = "weather_alert"
    case general = "general"
    case system = "system"
    
    var displayName: String {
        switch self {
        case .safetyAlert: return "Safety Alert"
        case .healthAdvisory: return "Health Advisory"
        case .travelUpdate: return "Travel Update"
        case .tripReminder: return "Trip Reminder"
        case .documentExpiry: return "Document Expiry"
        case .weatherAlert: return "Weather Alert"
        case .general: return "General"
        case .system: return "System"
        }
    }
}

// MARK: - Notification Priority Enum
enum NotificationPriority: String, Codable, CaseIterable {
    case critical = "critical"
    case high = "high"
    case medium = "medium"
    case low = "low"
    case info = "info"
    
    var displayName: String {
        switch self {
        case .critical: return "Critical"
        case .high: return "High"
        case .medium: return "Medium"
        case .low: return "Low"
        case .info: return "Information"
        }
    }
    
    var sortOrder: Int {
        switch self {
        case .critical: return 5
        case .high: return 4
        case .medium: return 3
        case .low: return 2
        case .info: return 1
        }
    }
}

// MARK: - Notification Extensions
extension TravelNotification {
    var timeAgoText: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: createdDate, relativeTo: Date())
    }
    
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: createdDate)
    }
    
    static func createSafetyAlert(
        for countryName: String,
        countryId: String,
        title: String,
        message: String,
        priority: NotificationPriority = .high
    ) -> TravelNotification {
        return TravelNotification(
            title: title,
            message: message,
            type: .safetyAlert,
            priority: priority,
            countryId: countryId,
            countryName: countryName
        )
    }
    
    static func createTripReminder(
        for trip: Trip,
        title: String,
        message: String,
        scheduledDate: Date? = nil
    ) -> TravelNotification {
        return TravelNotification(
            title: title,
            message: message,
            type: .tripReminder,
            priority: .medium,
            countryId: trip.countryId,
            countryName: trip.countryName,
            tripId: trip.id,
            scheduledDate: scheduledDate
        )
    }
    
    static func createHealthAdvisory(
        for countryName: String,
        countryId: String,
        title: String,
        message: String,
        priority: NotificationPriority = .medium
    ) -> TravelNotification {
        return TravelNotification(
            title: title,
            message: message,
            type: .healthAdvisory,
            priority: priority,
            countryId: countryId,
            countryName: countryName
        )
    }
}

