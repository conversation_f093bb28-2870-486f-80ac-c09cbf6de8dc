//
//  SafetyAlert.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import Foundation

/// Represents a safety alert for a specific country
struct SafetyAlert: Identifiable, Codable, Hashable {
    let id: String
    let type: AlertType
    let severity: AlertSeverity
    let title: String
    let description: String
    let issuedDate: Date
    let expiryDate: Date?
    let source: String
    let affectedRegions: [String]
    let recommendations: [String]
    
    init(
        id: String = UUID().uuidString,
        type: AlertType,
        severity: AlertSeverity,
        title: String,
        description: String,
        issuedDate: Date = Date(),
        expiryDate: Date? = nil,
        source: String,
        affectedRegions: [String] = [],
        recommendations: [String] = []
    ) {
        self.id = id
        self.type = type
        self.severity = severity
        self.title = title
        self.description = description
        self.issuedDate = issuedDate
        self.expiryDate = expiryDate
        self.source = source
        self.affectedRegions = affectedRegions
        self.recommendations = recommendations
    }
    
    var isActive: Bool {
        guard let expiryDate = expiryDate else { return true }
        return Date() < expiryDate
    }
    
    var iconName: String {
        switch type {
        case .war: return "exclamationmark.triangle.fill"
        case .terrorism: return "shield.slash.fill"
        case .epidemic: return "cross.case.fill"
        case .naturalDisaster: return "cloud.bolt.rain.fill"
        case .politicalUnrest: return "person.3.fill"
        case .crime: return "lock.slash.fill"
        case .health: return "heart.text.square.fill"
        case .travel: return "airplane.departure"
        case .general: return "info.circle.fill"
        }
    }
}

// MARK: - Alert Type Enum
enum AlertType: String, Codable, CaseIterable {
    case war = "war"
    case terrorism = "terrorism"
    case epidemic = "epidemic"
    case naturalDisaster = "natural_disaster"
    case politicalUnrest = "political_unrest"
    case crime = "crime"
    case health = "health"
    case travel = "travel"
    case general = "general"
    
    var displayName: String {
        switch self {
        case .war: return "War/Conflict"
        case .terrorism: return "Terrorism"
        case .epidemic: return "Epidemic/Disease"
        case .naturalDisaster: return "Natural Disaster"
        case .politicalUnrest: return "Political Unrest"
        case .crime: return "Crime"
        case .health: return "Health Advisory"
        case .travel: return "Travel Advisory"
        case .general: return "General Advisory"
        }
    }
}

// MARK: - Alert Severity Enum
enum AlertSeverity: String, Codable, CaseIterable {
    case critical = "critical"
    case high = "high"
    case medium = "medium"
    case low = "low"
    case info = "info"
    
    var displayName: String {
        switch self {
        case .critical: return "Critical"
        case .high: return "High"
        case .medium: return "Medium"
        case .low: return "Low"
        case .info: return "Information"
        }
    }
    
    var color: String {
        switch self {
        case .critical: return "red"
        case .high: return "orange"
        case .medium: return "yellow"
        case .low: return "blue"
        case .info: return "gray"
        }
    }
    
    var priority: Int {
        switch self {
        case .critical: return 5
        case .high: return 4
        case .medium: return 3
        case .low: return 2
        case .info: return 1
        }
    }
}
