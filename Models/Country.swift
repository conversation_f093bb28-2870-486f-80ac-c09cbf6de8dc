//
//  Country.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import Foundation
import SwiftData

/// Represents a country with comprehensive travel safety information
@Model
final class Country: Identifiable, Codable {
    @Attribute(.unique) var id: String
    var name: String
    var flagEmoji: String
    var descriptions: String
    var photoURL: String
    var continent: String
    var capital: String
    var population: Int
    var currency: String
    var language: String
    
    // Safety Information
    var overallSafetyRating: SafetyRating
    var alerts: [SafetyAlert]
    var healthInfo: HealthInformation
    var safetyInfo: SafetyInformation
    
    // Geographic Information
    var latitude: Double
    var longitude: Double
    var timeZone: String
    
    // Travel Requirements
    var visaRequired: Bool
    var vaccinationsRequired: [String]
    
    // Last Updated
    var lastUpdated: Date
    
    init(
        id: String,
        name: String,
        flagEmoji: String,
        description: String,
        photoURL: String,
        continent: String,
        capital: String,
        population: Int,
        currency: String,
        language: String,
        overallSafetyRating: SafetyRating,
        alerts: [SafetyAlert] = [],
        healthInfo: HealthInformation,
        safetyInfo: SafetyInformation,
        latitude: Double,
        longitude: Double,
        timeZone: String,
        visaRequired: Bool,
        vaccinationsRequired: [String] = [],
        lastUpdated: Date = Date()
    ) {
        self.id = id
        self.name = name
        self.flagEmoji = flagEmoji
        self.descriptions = description
        self.photoURL = photoURL
        self.continent = continent
        self.capital = capital
        self.population = population
        self.currency = currency
        self.language = language
        self.overallSafetyRating = overallSafetyRating
        self.alerts = alerts
        self.healthInfo = healthInfo
        self.safetyInfo = safetyInfo
        self.latitude = latitude
        self.longitude = longitude
        self.timeZone = timeZone
        self.visaRequired = visaRequired
        self.vaccinationsRequired = vaccinationsRequired
        self.lastUpdated = lastUpdated
    }
    
    // MARK: - Codable Implementation
    enum CodingKeys: String, CodingKey {
        case id, name, flagEmoji, description, photoURL, continent, capital
        case population, currency, language, overallSafetyRating, alerts
        case healthInfo, safetyInfo, latitude, longitude, timeZone
        case visaRequired, vaccinationsRequired, lastUpdated
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        self.id = try container.decode(String.self, forKey: .id)
        self.name = try container.decode(String.self, forKey: .name)
        self.flagEmoji = try container.decode(String.self, forKey: .flagEmoji)
        self.descriptions = try container.decode(String.self, forKey: .description)
        self.photoURL = try container.decode(String.self, forKey: .photoURL)
        self.continent = try container.decode(String.self, forKey: .continent)
        self.capital = try container.decode(String.self, forKey: .capital)
        self.population = try container.decode(Int.self, forKey: .population)
        self.currency = try container.decode(String.self, forKey: .currency)
        self.language = try container.decode(String.self, forKey: .language)
        self.overallSafetyRating = try container.decode(SafetyRating.self, forKey: .overallSafetyRating)
        self.alerts = try container.decode([SafetyAlert].self, forKey: .alerts)
        self.healthInfo = try container.decode(HealthInformation.self, forKey: .healthInfo)
        self.safetyInfo = try container.decode(SafetyInformation.self, forKey: .safetyInfo)
        self.latitude = try container.decode(Double.self, forKey: .latitude)
        self.longitude = try container.decode(Double.self, forKey: .longitude)
        self.timeZone = try container.decode(String.self, forKey: .timeZone)
        self.visaRequired = try container.decode(Bool.self, forKey: .visaRequired)
        self.vaccinationsRequired = try container.decode([String].self, forKey: .vaccinationsRequired)
        self.lastUpdated = try container.decode(Date.self, forKey: .lastUpdated)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(flagEmoji, forKey: .flagEmoji)
        try container.encode(descriptions, forKey: .description)
        try container.encode(photoURL, forKey: .photoURL)
        try container.encode(continent, forKey: .continent)
        try container.encode(capital, forKey: .capital)
        try container.encode(population, forKey: .population)
        try container.encode(currency, forKey: .currency)
        try container.encode(language, forKey: .language)
        try container.encode(overallSafetyRating, forKey: .overallSafetyRating)
        try container.encode(alerts, forKey: .alerts)
        try container.encode(healthInfo, forKey: .healthInfo)
        try container.encode(safetyInfo, forKey: .safetyInfo)
        try container.encode(latitude, forKey: .latitude)
        try container.encode(longitude, forKey: .longitude)
        try container.encode(timeZone, forKey: .timeZone)
        try container.encode(visaRequired, forKey: .visaRequired)
        try container.encode(vaccinationsRequired, forKey: .vaccinationsRequired)
        try container.encode(lastUpdated, forKey: .lastUpdated)
    }
}

// MARK: - Safety Rating Enum
enum SafetyRating: String, Codable, CaseIterable {
    case veryHigh = "very_high"
    case high = "high"
    case moderate = "moderate"
    case low = "low"
    case veryLow = "very_low"
    
    var displayName: String {
        switch self {
        case .veryHigh: return "Very High"
        case .high: return "High"
        case .moderate: return "Moderate"
        case .low: return "Low"
        case .veryLow: return "Very Low"
        }
    }
    
    var color: String {
        switch self {
        case .veryHigh: return "green"
        case .high: return "lightGreen"
        case .moderate: return "yellow"
        case .low: return "orange"
        case .veryLow: return "red"
        }
    }
}
