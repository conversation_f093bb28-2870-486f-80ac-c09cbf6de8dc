//
//  Trip.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import Foundation
import SwiftData

/// Represents a user's travel trip
@Model
final class Trip: Identifiable {
    @Attribute(.unique) var id: String
    var countryId: String
    var countryName: String
    var countryFlag: String
    var startDate: Date
    var endDate: Date
    var purpose: TripPurpose
    var status: TripStatus
    var notes: String
    var createdDate: Date
    var lastModified: Date
    
    // Computed properties
    var duration: Int {
        Calendar.current.dateComponents([.day], from: startDate, to: endDate).day ?? 0
    }
    
    var daysUntilTrip: Int {
        Calendar.current.dateComponents([.day], from: Date(), to: startDate).day ?? 0
    }
    
    var daysRemaining: Int {
        Calendar.current.dateComponents([.day], from: Date(), to: endDate).day ?? 0
    }
    
    var isUpcoming: Bool {
        startDate > Date()
    }
    
    var isActive: Bool {
        let now = Date()
        return startDate <= now && endDate >= now
    }
    
    var isPast: Bool {
        endDate < Date()
    }
    
    init(
        id: String = UUID().uuidString,
        countryId: String,
        countryName: String,
        countryFlag: String,
        startDate: Date,
        endDate: Date,
        purpose: TripPurpose,
        notes: String = "",
        createdDate: Date = Date()
    ) {
        self.id = id
        self.countryId = countryId
        self.countryName = countryName
        self.countryFlag = countryFlag
        self.startDate = startDate
        self.endDate = endDate
        self.purpose = purpose
        self.notes = notes
        self.createdDate = createdDate
        self.lastModified = createdDate
        
        // Auto-determine status based on dates
        self.status = Trip.determineStatus(startDate: startDate, endDate: endDate)
    }
    
    static func determineStatus(startDate: Date, endDate: Date) -> TripStatus {
        let now = Date()
        
        if endDate < now {
            return .completed
        } else if startDate <= now && endDate >= now {
            return .active
        } else {
            return .upcoming
        }
    }
    
    func updateStatus() {
        let newStatus = Trip.determineStatus(startDate: startDate, endDate: endDate)
        if status != newStatus {
            status = newStatus
            lastModified = Date()
        }
    }
    
    func updateTrip(
        countryId: String? = nil,
        countryName: String? = nil,
        countryFlag: String? = nil,
        startDate: Date? = nil,
        endDate: Date? = nil,
        purpose: TripPurpose? = nil,
        notes: String? = nil
    ) {
        if let countryId = countryId { self.countryId = countryId }
        if let countryName = countryName { self.countryName = countryName }
        if let countryFlag = countryFlag { self.countryFlag = countryFlag }
        if let startDate = startDate { self.startDate = startDate }
        if let endDate = endDate { self.endDate = endDate }
        if let purpose = purpose { self.purpose = purpose }
        if let notes = notes { self.notes = notes }
        
        updateStatus()
        lastModified = Date()
    }
}

// MARK: - Trip Purpose Enum
enum TripPurpose: String, Codable, CaseIterable {
    case business = "business"
    case leisure = "leisure"
    case education = "education"
    case medical = "medical"
    case family = "family"
    case volunteer = "volunteer"
    case research = "research"
    case other = "other"
    
    var displayName: String {
        switch self {
        case .business: return "Business"
        case .leisure: return "Leisure"
        case .education: return "Education"
        case .medical: return "Medical"
        case .family: return "Family Visit"
        case .volunteer: return "Volunteer Work"
        case .research: return "Research"
        case .other: return "Other"
        }
    }
    
    var iconName: String {
        switch self {
        case .business: return "briefcase.fill"
        case .leisure: return "sun.max.fill"
        case .education: return "graduationcap.fill"
        case .medical: return "cross.case.fill"
        case .family: return "person.3.fill"
        case .volunteer: return "heart.fill"
        case .research: return "magnifyingglass"
        case .other: return "questionmark.circle.fill"
        }
    }
}

// MARK: - Trip Status Enum
enum TripStatus: String, Codable, CaseIterable {
    case upcoming = "upcoming"
    case active = "active"
    case completed = "completed"
    case cancelled = "cancelled"
    
    var displayName: String {
        switch self {
        case .upcoming: return "Upcoming"
        case .active: return "Active"
        case .completed: return "Completed"
        case .cancelled: return "Cancelled"
        }
    }
    
    var color: String {
        switch self {
        case .upcoming: return "blue"
        case .active: return "green"
        case .completed: return "gray"
        case .cancelled: return "red"
        }
    }
    
    var iconName: String {
        switch self {
        case .upcoming: return "clock.fill"
        case .active: return "location.fill"
        case .completed: return "checkmark.circle.fill"
        case .cancelled: return "xmark.circle.fill"
        }
    }
}

// MARK: - Trip Extensions
extension Trip {
    var formattedDateRange: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        
        let startString = formatter.string(from: startDate)
        let endString = formatter.string(from: endDate)
        
        return "\(startString) - \(endString)"
    }
    
    var shortDateRange: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMM d"
        
        let startString = formatter.string(from: startDate)
        let endString = formatter.string(from: endDate)
        
        // If same year, don't repeat it
        let calendar = Calendar.current
        if calendar.component(.year, from: startDate) == calendar.component(.year, from: endDate) {
            return "\(startString) - \(endString)"
        } else {
            formatter.dateFormat = "MMM d, yyyy"
            let startStringWithYear = formatter.string(from: startDate)
            let endStringWithYear = formatter.string(from: endDate)
            return "\(startStringWithYear) - \(endStringWithYear)"
        }
    }
    
    var durationText: String {
        let days = duration
        if days == 1 {
            return "1 day"
        } else {
            return "\(days) days"
        }
    }
    
    var statusText: String {
        switch status {
        case .upcoming:
            if daysUntilTrip == 0 {
                return "Starts today"
            } else if daysUntilTrip == 1 {
                return "Starts tomorrow"
            } else {
                return "Starts in \(daysUntilTrip) days"
            }
        case .active:
            if daysRemaining == 0 {
                return "Ends today"
            } else if daysRemaining == 1 {
                return "Ends tomorrow"
            } else {
                return "\(daysRemaining) days remaining"
            }
        case .completed:
            return "Completed"
        case .cancelled:
            return "Cancelled"
        }
    }
}

