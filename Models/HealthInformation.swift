//
//  HealthInformation.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import Foundation

/// Represents health-related information for a country
struct HealthInformation: Codable, Hashable {
    let healthcareQuality: HealthcareQuality
    let recommendedVaccinations: [Vaccination]
    let requiredVaccinations: [Vaccination]
    let commonDiseases: [Disease]
    let waterSafety: WaterSafety
    let foodSafety: FoodSafety
    let airQuality: AirQuality
    let emergencyNumber: String
    let healthInsuranceRequired: Bool
    let medicalFacilities: [MedicalFacility]
    let healthTips: [String]
    
    init(
        healthcareQuality: HealthcareQuality,
        recommendedVaccinations: [Vaccination] = [],
        requiredVaccinations: [Vaccination] = [],
        commonDiseases: [Disease] = [],
        waterSafety: WaterSafety,
        foodSafety: FoodSafety,
        airQuality: AirQuality,
        emergencyNumber: String,
        healthInsuranceRequired: Bool,
        medicalFacilities: [MedicalFacility] = [],
        healthTips: [String] = []
    ) {
        self.healthcareQuality = healthcareQuality
        self.recommendedVaccinations = recommendedVaccinations
        self.requiredVaccinations = requiredVaccinations
        self.commonDiseases = commonDiseases
        self.waterSafety = waterSafety
        self.foodSafety = foodSafety
        self.airQuality = airQuality
        self.emergencyNumber = emergencyNumber
        self.healthInsuranceRequired = healthInsuranceRequired
        self.medicalFacilities = medicalFacilities
        self.healthTips = healthTips
    }
}

// MARK: - Healthcare Quality Enum
enum HealthcareQuality: String, Codable, CaseIterable {
    case excellent = "excellent"
    case good = "good"
    case adequate = "adequate"
    case poor = "poor"
    case veryPoor = "very_poor"
    
    var displayName: String {
        switch self {
        case .excellent: return "Excellent"
        case .good: return "Good"
        case .adequate: return "Adequate"
        case .poor: return "Poor"
        case .veryPoor: return "Very Poor"
        }
    }
    
    var color: String {
        switch self {
        case .excellent: return "green"
        case .good: return "lightGreen"
        case .adequate: return "yellow"
        case .poor: return "orange"
        case .veryPoor: return "red"
        }
    }
}

// MARK: - Vaccination Model
struct Vaccination: Identifiable, Codable, Hashable {
    let id: String
    let name: String
    let description: String
    let timeBeforeTravel: String // e.g., "4-6 weeks"
    let duration: String // e.g., "10 years"
    let isRequired: Bool
    
    init(
        id: String = UUID().uuidString,
        name: String,
        description: String,
        timeBeforeTravel: String,
        duration: String,
        isRequired: Bool
    ) {
        self.id = id
        self.name = name
        self.description = description
        self.timeBeforeTravel = timeBeforeTravel
        self.duration = duration
        self.isRequired = isRequired
    }
}

// MARK: - Disease Model
struct Disease: Identifiable, Codable, Hashable {
    let id: String
    let name: String
    let description: String
    let riskLevel: RiskLevel
    let prevention: [String]
    let symptoms: [String]
    
    init(
        id: String = UUID().uuidString,
        name: String,
        description: String,
        riskLevel: RiskLevel,
        prevention: [String] = [],
        symptoms: [String] = []
    ) {
        self.id = id
        self.name = name
        self.description = description
        self.riskLevel = riskLevel
        self.prevention = prevention
        self.symptoms = symptoms
    }
}

// MARK: - Risk Level Enum
enum RiskLevel: String, Codable, CaseIterable {
    case high = "high"
    case medium = "medium"
    case low = "low"
    
    var displayName: String {
        switch self {
        case .high: return "High Risk"
        case .medium: return "Medium Risk"
        case .low: return "Low Risk"
        }
    }
    
    var color: String {
        switch self {
        case .high: return "red"
        case .medium: return "orange"
        case .low: return "green"
        }
    }
}

// MARK: - Water Safety Enum
enum WaterSafety: String, Codable, CaseIterable {
    case safe = "safe"
    case boilRequired = "boil_required"
    case bottledOnly = "bottled_only"
    case unsafe = "unsafe"
    
    var displayName: String {
        switch self {
        case .safe: return "Safe to Drink"
        case .boilRequired: return "Boil Before Drinking"
        case .bottledOnly: return "Bottled Water Only"
        case .unsafe: return "Unsafe"
        }
    }
    
    var iconName: String {
        switch self {
        case .safe: return "drop.fill"
        case .boilRequired: return "flame.fill"
        case .bottledOnly: return "waterbottle.fill"
        case .unsafe: return "drop.slash.fill"
        }
    }
}

// MARK: - Food Safety Enum
enum FoodSafety: String, Codable, CaseIterable {
    case safe = "safe"
    case caution = "caution"
    case highRisk = "high_risk"
    
    var displayName: String {
        switch self {
        case .safe: return "Generally Safe"
        case .caution: return "Exercise Caution"
        case .highRisk: return "High Risk"
        }
    }
}

// MARK: - Air Quality Enum
enum AirQuality: String, Codable, CaseIterable {
    case good = "good"
    case moderate = "moderate"
    case unhealthy = "unhealthy"
    case hazardous = "hazardous"
    
    var displayName: String {
        switch self {
        case .good: return "Good"
        case .moderate: return "Moderate"
        case .unhealthy: return "Unhealthy"
        case .hazardous: return "Hazardous"
        }
    }
}

// MARK: - Medical Facility Model
struct MedicalFacility: Identifiable, Codable, Hashable {
    let id: String
    let name: String
    let type: FacilityType
    let address: String
    let phoneNumber: String
    let emergencyServices: Bool
    let englishSpeaking: Bool
    
    init(
        id: String = UUID().uuidString,
        name: String,
        type: FacilityType,
        address: String,
        phoneNumber: String,
        emergencyServices: Bool,
        englishSpeaking: Bool
    ) {
        self.id = id
        self.name = name
        self.type = type
        self.address = address
        self.phoneNumber = phoneNumber
        self.emergencyServices = emergencyServices
        self.englishSpeaking = englishSpeaking
    }
}

// MARK: - Facility Type Enum
enum FacilityType: String, Codable, CaseIterable {
    case hospital = "hospital"
    case clinic = "clinic"
    case pharmacy = "pharmacy"
    case emergencyRoom = "emergency_room"
    
    var displayName: String {
        switch self {
        case .hospital: return "Hospital"
        case .clinic: return "Clinic"
        case .pharmacy: return "Pharmacy"
        case .emergencyRoom: return "Emergency Room"
        }
    }
}
