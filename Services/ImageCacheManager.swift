//
//  ImageCacheManager.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import Foundation
import SwiftUI
import Combine

/// Advanced image caching and loading manager
@MainActor
final class ImageCacheManager: ObservableObject {
    static let shared = ImageCacheManager()
    
    private let cache = NSCache<NSString, UIImage>()
    private let fileManager = FileManager.default
    private let cacheDirectory: URL
    private var downloadTasks: [String: Task<UIImage?, Error>] = [:]
    
    // Configuration
    private let maxCacheSize: Int = 100 * 1024 * 1024 // 100MB
    private let maxCacheAge: TimeInterval = 7 * 24 * 60 * 60 // 7 days
    
    init() {
        // Setup cache directory
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        cacheDirectory = documentsPath.appendingPathComponent("ImageCache")
        
        // Create directory if needed
        try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
        
        // Configure memory cache
        cache.countLimit = 100
        cache.totalCostLimit = 50 * 1024 * 1024 // 50MB in memory
        
        // Setup cache cleanup
        setupCacheCleanup()
    }
    
    // MARK: - Public Methods
    
    /// Load image from URL with caching
    func loadImage(from urlString: String) async -> UIImage? {
        // Check memory cache first
        if let cachedImage = cache.object(forKey: urlString as NSString) {
            return cachedImage
        }
        
        // Check if already downloading
        if let existingTask = downloadTasks[urlString] {
            return try? await existingTask.value
        }
        
        // Check disk cache
        if let diskImage = loadImageFromDisk(urlString: urlString) {
            cache.setObject(diskImage, forKey: urlString as NSString)
            return diskImage
        }
        
        // Download image
        let downloadTask = Task<UIImage?, Error> {
            try await downloadImage(from: urlString)
        }
        
        downloadTasks[urlString] = downloadTask
        
        do {
            let image = try await downloadTask.value
            downloadTasks.removeValue(forKey: urlString)
            return image
        } catch {
            downloadTasks.removeValue(forKey: urlString)
            print("Failed to load image from \(urlString): \(error)")
            return nil
        }
    }
    
    /// Preload images for better performance
    func preloadImages(urls: [String]) {
        Task {
            await withTaskGroup(of: Void.self) { group in
                for url in urls {
                    group.addTask {
                        _ = await self.loadImage(from: url)
                    }
                }
            }
        }
    }
    
    /// Clear all cached images
    func clearCache() {
        cache.removeAllObjects()
        
        do {
            let files = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
            for file in files {
                try fileManager.removeItem(at: file)
            }
        } catch {
            print("Failed to clear image cache: \(error)")
        }
    }
    
    /// Get cache size information
    func getCacheInfo() -> CacheInfo {
        var totalSize: Int64 = 0
        var fileCount = 0
        
        do {
            let files = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: [URLResourceKey.fileSizeKey])
            fileCount = files.count
            
            for file in files {
                let attributes = try file.resourceValues(forKeys: [URLResourceKey.fileSizeKey])
                totalSize += Int64(attributes.fileSize ?? 0)
            }
        } catch {
            print("Failed to get cache info: \(error)")
        }
        
        return CacheInfo(
            fileCount: fileCount,
            totalSize: totalSize,
            formattedSize: ByteCountFormatter.string(fromByteCount: totalSize, countStyle: .file)
        )
    }
    
    // MARK: - Private Methods
    
    private func downloadImage(from urlString: String) async throws -> UIImage? {
        guard let url = URL(string: urlString) else {
            throw ImageCacheError.invalidURL
        }
        
        let (data, response) = try await URLSession.shared.data(from: url)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw ImageCacheError.invalidResponse
        }
        
        guard let image = UIImage(data: data) else {
            throw ImageCacheError.invalidImageData
        }
        
        // Cache in memory
        cache.setObject(image, forKey: urlString as NSString, cost: data.count)
        
        // Cache on disk
        saveImageToDisk(image: image, urlString: urlString)
        
        return image
    }
    
    private func loadImageFromDisk(urlString: String) -> UIImage? {
        let filename = urlString.addingPercentEncoding(withAllowedCharacters: .alphanumerics) ?? urlString
        let fileURL = cacheDirectory.appendingPathComponent(filename)
        
        guard fileManager.fileExists(atPath: fileURL.path) else {
            return nil
        }
        
        // Check if file is too old
        do {
            let attributes = try fileManager.attributesOfItem(atPath: fileURL.path)
            if let modificationDate = attributes[.modificationDate] as? Date {
                if Date().timeIntervalSince(modificationDate) > maxCacheAge {
                    try fileManager.removeItem(at: fileURL)
                    return nil
                }
            }
        } catch {
            return nil
        }
        
        return UIImage(contentsOfFile: fileURL.path)
    }
    
    private func saveImageToDisk(image: UIImage, urlString: String) {
        guard let data = image.jpegData(compressionQuality: 0.8) else { return }
        
        let filename = urlString.addingPercentEncoding(withAllowedCharacters: .alphanumerics) ?? urlString
        let fileURL = cacheDirectory.appendingPathComponent(filename)
        
        do {
            try data.write(to: fileURL)
        } catch {
            print("Failed to save image to disk: \(error)")
        }
    }
    
    private func setupCacheCleanup() {
        // Clean up old files on app launch
        Task {
            await cleanupOldFiles()
        }
        
        // Setup periodic cleanup
        Timer.scheduledTimer(withTimeInterval: 24 * 60 * 60, repeats: true) { _ in
            Task { @MainActor in
                await self.cleanupOldFiles()
            }
        }
    }
    
    private func cleanupOldFiles() async {
        do {
            let files = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: [URLResourceKey.contentModificationDateKey, URLResourceKey.fileSizeKey])
            
            var totalSize: Int64 = 0
            var filesToDelete: [URL] = []
            
            for file in files {
                let attributes = try file.resourceValues(forKeys: [URLResourceKey.contentModificationDateKey, URLResourceKey.fileSizeKey])
                
                // Check age
                if let modificationDate = attributes.contentModificationDate {
                    if Date().timeIntervalSince(modificationDate) > maxCacheAge {
                        filesToDelete.append(file)
                        continue
                    }
                }
                
                // Track size
                totalSize += Int64(attributes.fileSize ?? 0)
            }
            
            // Delete old files
            for file in filesToDelete {
                try fileManager.removeItem(at: file)
            }
            
            // If still over size limit, delete oldest files
            if totalSize > maxCacheSize {
                let sortedFiles = files.sorted { file1, file2 in
                    let date1 = (try? file1.resourceValues(forKeys: [URLResourceKey.contentModificationDateKey]))?.contentModificationDate ?? Date.distantPast
                    let date2 = (try? file2.resourceValues(forKeys: [URLResourceKey.contentModificationDateKey]))?.contentModificationDate ?? Date.distantPast
                    return date1 < date2
                }
                
                var currentSize = totalSize
                for file in sortedFiles {
                    if currentSize <= maxCacheSize { break }
                    
                    let size = (try? file.resourceValues(forKeys: [URLResourceKey.fileSizeKey]))?.fileSize ?? 0
                    try fileManager.removeItem(at: file)
                    currentSize -= Int64(size)
                }
            }
            
        } catch {
            print("Failed to cleanup cache: \(error)")
        }
    }
}

// MARK: - Cache Info
struct CacheInfo {
    let fileCount: Int
    let totalSize: Int64
    let formattedSize: String
}

// MARK: - Image Cache Error
enum ImageCacheError: LocalizedError {
    case invalidURL
    case invalidResponse
    case invalidImageData
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid image URL"
        case .invalidResponse:
            return "Invalid server response"
        case .invalidImageData:
            return "Invalid image data"
        }
    }
}

// MARK: - Cached Image View
struct CachedImageView: View {
    let url: String
    let placeholder: Image
    let contentMode: ContentMode
    
    @StateObject private var imageLoader = ImageLoader()
    
    init(url: String, placeholder: Image = Image(systemName: "photo"), contentMode: ContentMode = .fit) {
        self.url = url
        self.placeholder = placeholder
        self.contentMode = contentMode
    }
    
    var body: some View {
        Group {
            if let image = imageLoader.image {
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: contentMode)
            } else {
                placeholder
                    .foregroundColor(.secondary)
            }
        }
        .onAppear {
            imageLoader.loadImage(from: url)
        }
    }
}

// MARK: - Image Loader
@MainActor
final class ImageLoader: ObservableObject {
    @Published var image: UIImage?
    @Published var isLoading = false
    
    private var loadTask: Task<Void, Never>?
    
    func loadImage(from url: String) {
        guard image == nil && !isLoading else { return }
        
        isLoading = true
        
        loadTask = Task {
            let loadedImage = await ImageCacheManager.shared.loadImage(from: url)
            
            if !Task.isCancelled {
                self.image = loadedImage
                self.isLoading = false
            }
        }
    }
    
    deinit {
        loadTask?.cancel()
    }
}

