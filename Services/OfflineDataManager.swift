//
//  OfflineDataManager.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import Foundation
import Combine
import SwiftData
import Network

/// Manages offline data availability and synchronization
@MainActor
final class OfflineDataManager: ObservableObject {
    @Published var isOffline = false
    @Published var offlineDataAvailable = false
    @Published var pendingChanges: [PendingChange] = []
    
    private let modelContext: ModelContext
    private let networkMonitor = NWPathMonitor()
    private let offlineQueue = DispatchQueue(label: "com.safetravel.offline", qos: .background)
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
        setupNetworkMonitoring()
        checkOfflineDataAvailability()
        loadPendingChanges()
    }
    
    deinit {
        networkMonitor.cancel()
    }
    
    // MARK: - Public Methods
    
    /// Save data for offline use
    func saveForOfflineUse<T: PersistentModel & Codable>(items: [T], type: T.Type) async throws {
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        
        let data = try encoder.encode(items)
        let filename = "\(String(describing: type))_offline.json"
        
        try await saveOfflineData(data, filename: filename)
        updateOfflineDataAvailability()
    }
    
    /// Load offline data
    func loadOfflineData<T: Codable>(type: T.Type) async throws -> [T] {
        let filename = "\(String(describing: type))_offline.json"
        let data = try await getOfflineData(filename: filename)
        
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        
        return try decoder.decode([T].self, from: data)
    }
    
    /// Queue change for later synchronization
    func queueChange(_ change: PendingChange) {
        pendingChanges.append(change)
        savePendingChanges()
    }
    
    /// Process all pending changes when online
    func processPendingChanges() async {
        guard !isOffline && !pendingChanges.isEmpty else { return }
        
        var processedChanges: [PendingChange] = []
        
        for change in pendingChanges {
            do {
                try await processChange(change)
                processedChanges.append(change)
            } catch {
                print("Failed to process change: \(error)")
                // Keep the change in queue for retry
            }
        }
        
        // Remove successfully processed changes
        pendingChanges.removeAll { change in
            processedChanges.contains { $0.id == change.id }
        }
        
        savePendingChanges()
    }
    
    /// Get offline storage info
    func getOfflineStorageInfo() async -> OfflineStorageInfo {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let offlineDirectory = documentsPath.appendingPathComponent("OfflineData")
        
        var totalSize: Int64 = 0
        var fileCount = 0
        
        do {
            let files = try FileManager.default.contentsOfDirectory(at: offlineDirectory, includingPropertiesForKeys: [.fileSizeKey])
            fileCount = files.count
            
            for file in files {
                let attributes = try file.resourceValues(forKeys: [.fileSizeKey])
                totalSize += Int64(attributes.fileSize ?? 0)
            }
        } catch {
            // Directory might not exist yet
        }
        
        return OfflineStorageInfo(
            fileCount: fileCount,
            totalSize: totalSize,
            formattedSize: ByteCountFormatter.string(fromByteCount: totalSize, countStyle: .file),
            pendingChanges: pendingChanges.count
        )
    }
    
    /// Clear all offline data
    func clearOfflineData() async throws {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let offlineDirectory = documentsPath.appendingPathComponent("OfflineData")
        
        if FileManager.default.fileExists(atPath: offlineDirectory.path) {
            try FileManager.default.removeItem(at: offlineDirectory)
        }
        
        pendingChanges.removeAll()
        savePendingChanges()
        updateOfflineDataAvailability()
    }
    
    // MARK: - Private Methods
    
    private func setupNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            Task { @MainActor in
                let wasOffline = self?.isOffline ?? false
                self?.isOffline = path.status != .satisfied
                
                // Process pending changes when coming back online
                if wasOffline && path.status == .satisfied {
                    await self?.processPendingChanges()
                }
            }
        }
        
        let queue = DispatchQueue(label: "NetworkMonitor")
        networkMonitor.start(queue: queue)
    }
    
    private func checkOfflineDataAvailability() {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let offlineDirectory = documentsPath.appendingPathComponent("OfflineData")
        
        offlineDataAvailable = FileManager.default.fileExists(atPath: offlineDirectory.path)
    }
    
    private func updateOfflineDataAvailability() {
        checkOfflineDataAvailability()
    }
    
    private func saveOfflineData(_ data: Data, filename: String) async throws {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let offlineDirectory = documentsPath.appendingPathComponent("OfflineData")
        
        // Create directory if needed
        try FileManager.default.createDirectory(at: offlineDirectory, withIntermediateDirectories: true)
        
        let fileURL = offlineDirectory.appendingPathComponent(filename)
        try data.write(to: fileURL)
    }
    
    private func getOfflineData(filename: String) async throws -> Data {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let offlineDirectory = documentsPath.appendingPathComponent("OfflineData")
        let fileURL = offlineDirectory.appendingPathComponent(filename)
        
        return try Data(contentsOf: fileURL)
    }
    
    private func loadPendingChanges() {
        if let data = UserDefaults.standard.data(forKey: "PendingChanges") {
            do {
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                pendingChanges = try decoder.decode([PendingChange].self, from: data)
            } catch {
                print("Failed to load pending changes: \(error)")
                pendingChanges = []
            }
        }
    }
    
    private func savePendingChanges() {
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(pendingChanges)
            UserDefaults.standard.set(data, forKey: "PendingChanges")
        } catch {
            print("Failed to save pending changes: \(error)")
        }
    }
    
    private func processChange(_ change: PendingChange) async throws {
        switch change.type {
        case .createTrip:
            try await processCreateTrip(change)
        case .updateTrip:
            try await processUpdateTrip(change)
        case .deleteTrip:
            try await processDeleteTrip(change)
        case .createNotification:
            try await processCreateNotification(change)
        case .updateNotification:
            try await processUpdateNotification(change)
        case .deleteNotification:
            try await processDeleteNotification(change)
        }
    }
    
    private func processCreateTrip(_ change: PendingChange) async throws {
        guard let tripData = change.data,
              let trip = try? JSONDecoder().decode(Trip.self, from: tripData) else {
            throw OfflineError.invalidChangeData
        }
        
        modelContext.insert(trip)
        try modelContext.save()
    }
    
    private func processUpdateTrip(_ change: PendingChange) async throws {
        guard let tripData = change.data,
              let updatedTrip = try? JSONDecoder().decode(Trip.self, from: tripData) else {
            throw OfflineError.invalidChangeData
        }
        
        let descriptor = FetchDescriptor<Trip>(
            predicate: #Predicate<Trip> { trip in
                trip.id == updatedTrip.id
            }
        )
        
        if let existingTrip = try modelContext.fetch(descriptor).first {
            existingTrip.updateTrip(
                startDate: updatedTrip.startDate,
                endDate: updatedTrip.endDate,
                purpose: updatedTrip.purpose,
                notes: updatedTrip.notes
            )
            try modelContext.save()
        }
    }
    
    private func processDeleteTrip(_ change: PendingChange) async throws {
        guard let tripId = change.entityId else {
            throw OfflineError.missingEntityId
        }
        
        let descriptor = FetchDescriptor<Trip>(
            predicate: #Predicate<Trip> { trip in
                trip.id == tripId
            }
        )
        
        if let trip = try modelContext.fetch(descriptor).first {
            modelContext.delete(trip)
            try modelContext.save()
        }
    }
    
    private func processCreateNotification(_ change: PendingChange) async throws {
        guard let notificationData = change.data,
              let notification = try? JSONDecoder().decode(TravelNotification.self, from: notificationData) else {
            throw OfflineError.invalidChangeData
        }
        
        modelContext.insert(notification)
        try modelContext.save()
    }
    
    private func processUpdateNotification(_ change: PendingChange) async throws {
        guard let notificationData = change.data,
              let updatedNotification = try? JSONDecoder().decode(TravelNotification.self, from: notificationData) else {
            throw OfflineError.invalidChangeData
        }
        
        let descriptor = FetchDescriptor<TravelNotification>(
            predicate: #Predicate<TravelNotification> { notification in
                notification.id == updatedNotification.id
            }
        )
        
        if let existingNotification = try modelContext.fetch(descriptor).first {
            existingNotification.isRead = updatedNotification.isRead
            try modelContext.save()
        }
    }
    
    private func processDeleteNotification(_ change: PendingChange) async throws {
        guard let notificationId = change.entityId else {
            throw OfflineError.missingEntityId
        }
        
        let descriptor = FetchDescriptor<TravelNotification>(
            predicate: #Predicate<TravelNotification> { notification in
                notification.id == notificationId
            }
        )
        
        if let notification = try modelContext.fetch(descriptor).first {
            modelContext.delete(notification)
            try modelContext.save()
        }
    }
}

// MARK: - Pending Change
struct PendingChange: Codable, Identifiable {
    let id = UUID()
    let type: ChangeType
    let entityId: String?
    let data: Data?
    let timestamp: Date
    
    init(type: ChangeType, entityId: String? = nil, data: Data? = nil) {
        self.type = type
        self.entityId = entityId
        self.data = data
        self.timestamp = Date()
    }
}

// MARK: - Change Type
enum ChangeType: String, Codable, CaseIterable {
    case createTrip
    case updateTrip
    case deleteTrip
    case createNotification
    case updateNotification
    case deleteNotification
}

// MARK: - Offline Storage Info
struct OfflineStorageInfo {
    let fileCount: Int
    let totalSize: Int64
    let formattedSize: String
    let pendingChanges: Int
}

// MARK: - Offline Error
enum OfflineError: LocalizedError {
    case invalidChangeData
    case missingEntityId
    case processingFailed
    
    var errorDescription: String? {
        switch self {
        case .invalidChangeData:
            return "Invalid change data"
        case .missingEntityId:
            return "Missing entity ID"
        case .processingFailed:
            return "Failed to process offline change"
        }
    }
}

