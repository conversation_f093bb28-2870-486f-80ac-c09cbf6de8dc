//
//  CountryDataService.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import Foundation
import SwiftData
import Combine

/// Service responsible for loading and managing country data
@MainActor
final class CountryDataService: ObservableObject {
    @Published var countries: [Country] = []
    @Published var isLoading = false
    @Published var errorMessage: String?

    private let modelContext: ModelContext
    private let dataSyncManager: DataSyncManager
    private let offlineDataManager: OfflineDataManager

    init(modelContext: ModelContext) {
        self.modelContext = modelContext
        self.dataSyncManager = DataSyncManager(modelContext: modelContext)
        self.offlineDataManager = OfflineDataManager(modelContext: modelContext)
    }
    
    // MARK: - Public Methods
    
    /// Load countries with advanced caching and offline support
    func loadCountries() async {
        isLoading = true
        errorMessage = nil

        do {
            // Try offline data first if offline
            if offlineDataManager.isOffline && offlineDataManager.offlineDataAvailable {
                let offlineCountries: [Country] = try await offlineDataManager.loadOfflineData(type: Country.self)
                countries = offlineCountries
                isLoading = false
                return
            }

            // Use data sync manager for online loading
            await dataSyncManager.performSync()

            // Load from database
            let storedCountries = try await loadCountriesFromDatabase()
            countries = storedCountries

            // Save for offline use
            if !countries.isEmpty {
                try await offlineDataManager.saveForOfflineUse(items: countries, type: Country.self)
            }

        } catch {
            errorMessage = "Failed to load countries: \(error.localizedDescription)"
            print("Error loading countries: \(error)")

            // Try to load cached data as fallback
            do {
                let cachedCountries = try offlineDataManager.getCachedCountries()
                if !cachedCountries.isEmpty {
                    countries = cachedCountries
                    errorMessage = "Loaded from cache (offline mode)"
                }
            } catch {
                print("Failed to load cached countries: \(error)")
            }
        }

        isLoading = false
    }
    
    /// Refresh countries with force sync
    func refreshCountries() async {
        isLoading = true
        errorMessage = nil

        do {
            // Force refresh through sync manager
            await dataSyncManager.forceRefresh()

            // Reload from database
            let refreshedCountries = try await loadCountriesFromDatabase()
            countries = refreshedCountries

            // Update offline cache
            if !countries.isEmpty {
                try await offlineDataManager.saveForOfflineUse(items: countries, type: Country.self)
            }

        } catch {
            errorMessage = "Failed to refresh countries: \(error.localizedDescription)"
            print("Error refreshing countries: \(error)")
        }

        isLoading = false
    }
    
    /// Get country by ID
    func getCountry(by id: String) -> Country? {
        return countries.first { $0.id == id }
    }
    
    /// Search countries by name
    func searchCountries(query: String) -> [Country] {
        if query.isEmpty {
            return countries
        }

        let results = countries.filter { country in
            country.name.localizedCaseInsensitiveContains(query) ||
            country.continent.localizedCaseInsensitiveContains(query) ||
            country.capital.localizedCaseInsensitiveContains(query)
        }

        // Limit results for performance
        return Array(results.prefix(Constants.Data.maxSearchResults))
    }
    
    /// Filter countries by safety rating
    func filterCountries(by safetyRating: SafetyRating) -> [Country] {
        return countries.filter { $0.overallSafetyRating == safetyRating }
    }
    
    /// Get countries by continent
    func getCountries(by continent: String) -> [Country] {
        return countries.filter { $0.continent == continent }
    }
    
    /// Get countries with active alerts
    func getCountriesWithAlerts() -> [Country] {
        return countries.filter { country in
            country.alerts.contains { $0.isActive }
        }
    }
    
    // MARK: - Private Methods
    
    private func loadCountriesFromJSON() async throws -> [Country] {
        do {
            let countries: [Country] = try Bundle.main.decode([Country].self, from: Constants.Data.countriesFileName)
            UserDefaults.standard.lastDataRefresh = Date()
            return countries
        } catch {
            print("JSON Decoding Error: \(error)")
            throw CountryDataError.decodingFailed(error)
        }
    }
    
    private func loadCountriesFromDatabase() async throws -> [Country] {
        let descriptor = FetchDescriptor<Country>(
            sortBy: [SortDescriptor(\.name)]
        )
        
        return try modelContext.fetch(descriptor)
    }
    
    private func clearDatabase() async throws {
        let descriptor = FetchDescriptor<Country>()
        let existingCountries = try modelContext.fetch(descriptor)
        
        for country in existingCountries {
            modelContext.delete(country)
        }
        
        try modelContext.save()
    }
}

// MARK: - Error Types
enum CountryDataError: LocalizedError {
    case fileNotFound
    case decodingFailed(Error)
    case databaseError(Error)
    
    var errorDescription: String? {
        switch self {
        case .fileNotFound:
            return "Countries data file not found"
        case .decodingFailed(let error):
            return "Failed to decode countries data: \(error.localizedDescription)"
        case .databaseError(let error):
            return "Database error: \(error.localizedDescription)"
        }
    }
}

// MARK: - Extensions
extension CountryDataService {
    /// Get continents list
    var continents: [String] {
        Array(Set(countries.map { $0.continent })).sorted()
    }

    /// Sync status from data sync manager
    var syncStatus: SyncStatus {
        dataSyncManager.syncStatus
    }

    /// Offline status
    var isOffline: Bool {
        offlineDataManager.isOffline
    }

    /// Offline data availability
    var hasOfflineData: Bool {
        offlineDataManager.offlineDataAvailable
    }
    
    /// Get safety ratings distribution
    var safetyRatingsDistribution: [SafetyRating: Int] {
        var distribution: [SafetyRating: Int] = [:]
        
        for rating in SafetyRating.allCases {
            distribution[rating] = countries.filter { $0.overallSafetyRating == rating }.count
        }
        
        return distribution
    }
    
    /// Get countries sorted by safety rating (safest first)
    var countriesBySafety: [Country] {
        countries.sorted { country1, country2 in
            let rating1 = SafetyRating.allCases.firstIndex(of: country1.overallSafetyRating) ?? 0
            let rating2 = SafetyRating.allCases.firstIndex(of: country2.overallSafetyRating) ?? 0
            return rating1 < rating2
        }
    }
    
    /// Get countries with critical alerts
    var countriesWithCriticalAlerts: [Country] {
        countries.filter { country in
            country.alerts.contains { $0.severity == .critical && $0.isActive }
        }
    }
}

