//
//  DataSyncManager.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import Foundation
import SwiftData
import Network
import Combine
import UIKit

/// Advanced data synchronization manager with caching and offline support
@MainActor
final class DataSyncManager: ObservableObject {
    @Published var syncStatus: SyncStatus = .idle
    @Published var lastSyncDate: Date?
    @Published var isOfflineMode = false
    
    private let modelContext: ModelContext
    private let networkMonitor = NWPathMonitor()
    private let syncQueue = DispatchQueue(label: "com.safetravel.sync", qos: .background)
    private var syncTask: Task<Void, Never>?
    
    // Cache management
    private let cacheManager = CacheManager()
    private let backgroundTaskManager = BackgroundTaskManager()
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
        self.lastSyncDate = UserDefaults.standard.lastDataRefresh
        setupNetworkMonitoring()
        scheduleBackgroundSync()
    }
    
    deinit {
        networkMonitor.cancel()
        syncTask?.cancel()
    }
    
    // MARK: - Public Methods
    
    /// Perform full data synchronization
    func performSync(force: Bool = false) async {
        guard !syncStatus.isSyncing else { return }
        
        syncStatus = .syncing
        
        do {
            // Check if sync is needed
            if !force && !shouldSync() {
                syncStatus = .upToDate
                return
            }
            
            // Load and cache data
            let countries = try await loadCountriesWithCaching()
            
            // Update database
            try await updateDatabase(with: countries)
            
            // Update sync status
            lastSyncDate = Date()
            UserDefaults.standard.lastDataRefresh = lastSyncDate
            syncStatus = .completed
            
            // Schedule next background sync
            scheduleBackgroundSync()
            
        } catch {
            syncStatus = .failed(error)
            print("Sync failed: \(error)")
        }
    }
    
    /// Force refresh data from source
    func forceRefresh() async {
        await performSync(force: true)
    }
    
    /// Get cached countries for offline use
    func getCachedCountries() throws -> [Country] {
        let descriptor = FetchDescriptor<Country>(
            sortBy: [SortDescriptor(\.name)]
        )
        return try modelContext.fetch(descriptor)
    }
    
    /// Check if data is available offline
    var hasOfflineData: Bool {
        do {
            let countries = try getCachedCountries()
            return !countries.isEmpty
        } catch {
            return false
        }
    }
    
    // MARK: - Private Methods
    
    private func setupNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            Task { @MainActor in
                self?.isOfflineMode = path.status != .satisfied
                
                // Auto-sync when coming back online
                if path.status == .satisfied && self?.isOfflineMode == false {
                    await self?.performSync()
                }
            }
        }
        
        let queue = DispatchQueue(label: "NetworkMonitor")
        networkMonitor.start(queue: queue)
    }
    
    private func shouldSync() -> Bool {
        guard let lastSync = lastSyncDate else { return true }
        
        let timeSinceLastSync = Date().timeIntervalSince(lastSync)
        return timeSinceLastSync > Constants.Data.dataRefreshInterval
    }
    
    private func loadCountriesWithCaching() async throws -> [Country] {
        // Try to load from cache first
        if let cachedData = await cacheManager.getCachedCountries(),
           !shouldSync() {
            return cachedData
        }
        
        // Load from bundle
        let countries: [Country] = try Bundle.main.decode([Country].self, from: Constants.Data.countriesFileName)
        
        // Cache the data
        await cacheManager.cacheCountries(countries)
        
        return countries
    }
    
    private func updateDatabase(with countries: [Country]) async throws {
        // Clear existing data
        let descriptor = FetchDescriptor<Country>()
        let existingCountries = try modelContext.fetch(descriptor)
        
        for country in existingCountries {
            modelContext.delete(country)
        }
        
        // Insert new data
        for country in countries {
            modelContext.insert(country)
        }
        
        try modelContext.save()
    }
    
    private func scheduleBackgroundSync() {
        backgroundTaskManager.scheduleBackgroundRefresh { [weak self] in
            await self?.performSync()
        }
    }
}

// MARK: - Sync Status
enum SyncStatus: Equatable {
    case idle
    case syncing
    case completed
    case upToDate
    case failed(Error)
    
    var isSyncing: Bool {
        if case .syncing = self {
            return true
        }
        return false
    }
    
    var displayText: String {
        switch self {
        case .idle:
            return "Ready to sync"
        case .syncing:
            return "Syncing..."
        case .completed:
            return "Sync completed"
        case .upToDate:
            return "Up to date"
        case .failed:
            return "Sync failed"
        }
    }
    
    static func == (lhs: SyncStatus, rhs: SyncStatus) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle), (.syncing, .syncing), (.completed, .completed), (.upToDate, .upToDate):
            return true
        case (.failed, .failed):
            return true
        default:
            return false
        }
    }
}

// MARK: - Cache Manager
actor CacheManager {
    private let cacheDirectory: URL
    private let countriesCacheFile = "countries_cache.json"
    
    init() {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        cacheDirectory = documentsPath.appendingPathComponent("SafeTravelCache")
        
        // Create cache directory if needed
        try? FileManager.default.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
    }
    
    func cacheCountries(_ countries: [Country]) async {
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(countries)
            
            let cacheURL = cacheDirectory.appendingPathComponent(countriesCacheFile)
            try data.write(to: cacheURL)
            
            print("Countries cached successfully")
        } catch {
            print("Failed to cache countries: \(error)")
        }
    }
    
    func getCachedCountries() async -> [Country]? {
        do {
            let cacheURL = cacheDirectory.appendingPathComponent(countriesCacheFile)
            
            guard FileManager.default.fileExists(atPath: cacheURL.path) else {
                return nil
            }
            
            let data = try Data(contentsOf: cacheURL)
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            
            let countries = try decoder.decode([Country].self, from: data)
            return countries
        } catch {
            print("Failed to load cached countries: \(error)")
            return nil
        }
    }
    
    func clearCache() async {
        do {
            let cacheURL = cacheDirectory.appendingPathComponent(countriesCacheFile)
            try FileManager.default.removeItem(at: cacheURL)
            print("Cache cleared successfully")
        } catch {
            print("Failed to clear cache: \(error)")
        }
    }
    
    func getCacheSize() async -> Int64 {
        do {
            let cacheURL = cacheDirectory.appendingPathComponent(countriesCacheFile)
            let attributes = try FileManager.default.attributesOfItem(atPath: cacheURL.path)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            return 0
        }
    }
}

// MARK: - Background Task Manager
final class BackgroundTaskManager {
    private var backgroundTask: UIBackgroundTaskIdentifier = UIBackgroundTaskIdentifier.invalid
    
    func scheduleBackgroundRefresh(completion: @escaping () async -> Void) {
        // Register for background app refresh
        Task {
            await UIApplication.shared.setMinimumBackgroundFetchInterval(UIApplication.backgroundFetchIntervalMinimum)
        }
        
        // Schedule background task
        backgroundTask = UIApplication.shared.beginBackgroundTask { [weak self] in
            self?.endBackgroundTask()
        }
        
        Task {
            await completion()
            endBackgroundTask()
        }
    }
    
    private func endBackgroundTask() {
        if backgroundTask != UIBackgroundTaskIdentifier.invalid {
            UIApplication.shared.endBackgroundTask(backgroundTask)
            backgroundTask = UIBackgroundTaskIdentifier.invalid
        }
    }
}
