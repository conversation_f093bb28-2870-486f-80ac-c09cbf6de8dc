---
type: "always_apply"
---

# SafeTravel iOS App Development Rules

## Core Standards
- Swift 6 with strict concurrency, iOS 18.0+ minimum
- SwiftUI + MVVM architecture + SwiftData persistence
- Use @MainActor for UI operations, async/await for data
- Follow Apple HIG and Swift API Design Guidelines

## Code Quality
- Descriptive naming, no abbreviations
- Functions under 25 lines, proper error handling
- Document public APIs, use dependency injection
- Organize: Models/, Views/, ViewModels/, Services/, Resources/

## SwiftUI Rules
- @StateObject for ViewModels, @ObservedObject for passed objects
- NavigationStack (not NavigationView), proper state management
- LazyVStack/LazyHStack for large lists, implement loading states
- Native animations, dark mode support, SF Symbols

## Data Management
- JSON with comprehensive error handling and Codable
- Cache images/data efficiently, validate all user input
- SwiftData relationships with @Relationship attribute
- Use async/await for all data operations

## Performance
- Debounced search, efficient image caching
- Profile with Instruments, minimize view recomposition

## App-Specific Rules
- Countries JSON: name, flag, description, photo, alerts, health, safety
- Trip validation: end date after start date, automatic status updates
- MapKit with safety indicators, handle location permissions
- Search: debounced input, empty states, text + list browsing

## Testing & Quality
- Never force unwrap optionals, user-friendly error messages
- Profile performance, check memory leaks, thread safety