//
//  TripsViewModel.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import Foundation
import SwiftData
import Combine

/// ViewModel for managing trips functionality
@MainActor
final class TripsViewModel: ObservableObject {
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let modelContext: ModelContext
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }
    
    // MARK: - Trip Management
    
    func createTrip(
        countryId: String,
        countryName: String,
        countryFlag: String,
        startDate: Date,
        endDate: Date,
        purpose: TripPurpose,
        notes: String = ""
    ) throws {
        let trip = Trip(
            countryId: countryId,
            countryName: countryName,
            countryFlag: countryFlag,
            startDate: startDate,
            endDate: endDate,
            purpose: purpose,
            notes: notes
        )
        
        modelContext.insert(trip)
        try modelContext.save()
    }
    
    func updateTrip(_ trip: Trip) throws {
        trip.updateStatus()
        try modelContext.save()
    }
    
    func deleteTrip(_ trip: Trip) throws {
        modelContext.delete(trip)
        try modelContext.save()
    }
    
    func updateAllTripStatuses() throws {
        let descriptor = FetchDescriptor<Trip>()
        let trips = try modelContext.fetch(descriptor)
        
        for trip in trips {
            trip.updateStatus()
        }
        
        try modelContext.save()
    }
    
    // MARK: - Trip Filtering
    
    func getActiveTrips() throws -> [Trip] {
        let now = Date()
        let descriptor = FetchDescriptor<Trip>(
            predicate: #Predicate<Trip> { trip in
                trip.startDate <= now && trip.endDate >= now
            },
            sortBy: [SortDescriptor(\.startDate)]
        )
        
        return try modelContext.fetch(descriptor)
    }
    
    func getUpcomingTrips() throws -> [Trip] {
        let now = Date()
        let descriptor = FetchDescriptor<Trip>(
            predicate: #Predicate<Trip> { trip in
                trip.startDate > now
            },
            sortBy: [SortDescriptor(\.startDate)]
        )
        
        return try modelContext.fetch(descriptor)
    }
    
    func getPastTrips() throws -> [Trip] {
        let now = Date()
        let descriptor = FetchDescriptor<Trip>(
            predicate: #Predicate<Trip> { trip in
                trip.endDate < now
            },
            sortBy: [SortDescriptor(\.endDate, order: .reverse)]
        )
        
        return try modelContext.fetch(descriptor)
    }
    
    func getTripsForCountry(_ countryId: String) throws -> [Trip] {
        let descriptor = FetchDescriptor<Trip>(
            predicate: #Predicate<Trip> { trip in
                trip.countryId == countryId
            },
            sortBy: [SortDescriptor(\.startDate, order: .reverse)]
        )
        
        return try modelContext.fetch(descriptor)
    }
}

