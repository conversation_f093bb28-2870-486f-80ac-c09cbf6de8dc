//
//  NotificationViewModel.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import Foundation
import SwiftData
import Combine

/// ViewModel for managing notifications functionality
@MainActor
final class NotificationViewModel: ObservableObject {
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let modelContext: ModelContext
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }
    
    // MARK: - Notification Management
    
    func createNotification(
        title: String,
        message: String,
        type: NotificationType,
        priority: NotificationPriority,
        countryId: String? = nil,
        countryName: String? = nil,
        tripId: String? = nil,
        scheduledDate: Date? = nil,
        expiryDate: Date? = nil
    ) throws {
        let notification = TravelNotification(
            title: title,
            message: message,
            type: type,
            priority: priority,
            countryId: countryId,
            countryName: countryName,
            tripId: tripId,
            scheduledDate: scheduledDate,
            expiryDate: expiryDate
        )
        
        modelContext.insert(notification)
        try modelContext.save()
    }
    
    func markAsRead(_ notification: TravelNotification) throws {
        notification.markAsRead()
        try modelContext.save()
    }
    
    func markAsUnread(_ notification: TravelNotification) throws {
        notification.markAsUnread()
        try modelContext.save()
    }
    
    func deleteNotification(_ notification: TravelNotification) throws {
        modelContext.delete(notification)
        try modelContext.save()
    }
    
    func markAllAsRead() throws {
        let descriptor = FetchDescriptor<TravelNotification>(
            predicate: #Predicate<TravelNotification> { notification in
                !notification.isRead
            }
        )
        
        let unreadNotifications = try modelContext.fetch(descriptor)
        
        for notification in unreadNotifications {
            notification.markAsRead()
        }
        
        try modelContext.save()
    }
    
    func clearReadNotifications() throws {
        let descriptor = FetchDescriptor<TravelNotification>(
            predicate: #Predicate<TravelNotification> { notification in
                notification.isRead
            }
        )
        
        let readNotifications = try modelContext.fetch(descriptor)
        
        for notification in readNotifications {
            modelContext.delete(notification)
        }
        
        try modelContext.save()
    }
    
    // MARK: - Notification Filtering
    
    func getUnreadNotifications() throws -> [TravelNotification] {
        let now = Date()
        let descriptor = FetchDescriptor<TravelNotification>(
            predicate: #Predicate<TravelNotification> { notification in
                (!notification.isRead && notification.expiryDate == nil) || (notification.expiryDate != nil && notification.expiryDate! > now)
            },
            sortBy: [SortDescriptor(\.createdDate, order: .reverse)]
        )
        return try modelContext.fetch(descriptor)
    }
    
    func getNotificationsByType(_ type: NotificationType) throws -> [TravelNotification] {
        let descriptor = FetchDescriptor<TravelNotification>(
            predicate: #Predicate<TravelNotification> { notification in
                notification.type == type
            },
            sortBy: [SortDescriptor(\.createdDate, order: .reverse)]
        )
        
        return try modelContext.fetch(descriptor)
    }
    
    func getNotificationsByPriority(_ priority: NotificationPriority) throws -> [TravelNotification] {
        let descriptor = FetchDescriptor<TravelNotification>(
            predicate: #Predicate<TravelNotification> { notification in
                notification.priority == priority
            },
            sortBy: [SortDescriptor(\.createdDate, order: .reverse)]
        )
        
        return try modelContext.fetch(descriptor)
    }
    
    func getNotificationsForCountry(_ countryId: String) throws -> [TravelNotification] {
        let descriptor = FetchDescriptor<TravelNotification>(
            predicate: #Predicate<TravelNotification> { notification in
                notification.countryId == countryId
            },
            sortBy: [SortDescriptor(\.createdDate, order: .reverse)]
        )
        
        return try modelContext.fetch(descriptor)
    }
    
    func getNotificationsForTrip(_ tripId: String) throws -> [TravelNotification] {
        let descriptor = FetchDescriptor<TravelNotification>(
            predicate: #Predicate<TravelNotification> { notification in
                notification.tripId == tripId
            },
            sortBy: [SortDescriptor(\.createdDate, order: .reverse)]
        )
        
        return try modelContext.fetch(descriptor)
    }
    
    // MARK: - Automatic Notifications
    
    func createTripReminders(for trip: Trip) throws {
        // Create reminder 1 week before trip
        if let oneWeekBefore = Calendar.current.date(byAdding: .day, value: -7, to: trip.startDate),
           oneWeekBefore > Date() {
            try createNotification(
                title: "Trip Reminder",
                message: "Your trip to \(trip.countryName) starts in one week. Don't forget to check for any travel updates!",
                type: .tripReminder,
                priority: .medium,
                countryId: trip.countryId,
                countryName: trip.countryName,
                tripId: trip.id,
                scheduledDate: oneWeekBefore
            )
        }
        
        // Create reminder 1 day before trip
        if let oneDayBefore = Calendar.current.date(byAdding: .day, value: -1, to: trip.startDate),
           oneDayBefore > Date() {
            try createNotification(
                title: "Trip Starting Tomorrow",
                message: "Your trip to \(trip.countryName) starts tomorrow. Have a safe journey!",
                type: .tripReminder,
                priority: .high,
                countryId: trip.countryId,
                countryName: trip.countryName,
                tripId: trip.id,
                scheduledDate: oneDayBefore
            )
        }
    }
    
    func createSafetyAlertNotification(for country: Country, alert: SafetyAlert) throws {
        try createNotification(
            title: "Safety Alert: \(country.name)",
            message: alert.title,
            type: .safetyAlert,
            priority: alert.severity == .critical ? .critical : .high,
            countryId: country.id,
            countryName: country.name
        )
    }
}

