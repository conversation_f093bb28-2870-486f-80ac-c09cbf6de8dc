//
//  Extensions.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import Foundation
import SwiftUI

// MARK: - Date Extensions
extension Date {
    /// Returns a formatted string for display in the UI
    var displayString: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: self)
    }
    
    /// Returns a short formatted string for compact display
    var shortDisplayString: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMM d"
        return formatter.string(from: self)
    }
    
    /// Returns a relative time string (e.g., "2 days ago", "in 3 hours")
    var relativeString: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: self, relativeTo: Date())
    }
    
    /// Check if date is today
    var isToday: Bool {
        Calendar.current.isDateInToday(self)
    }
    
    /// Check if date is tomorrow
    var isTomorrow: Bool {
        Calendar.current.isDateInTomorrow(self)
    }
    
    /// Check if date is yesterday
    var isYesterday: Bool {
        Calendar.current.isDateInYesterday(self)
    }
    
    /// Days between this date and another date
    func daysBetween(_ otherDate: Date) -> Int {
        Calendar.current.dateComponents([.day], from: self, to: otherDate).day ?? 0
    }
}

// MARK: - Color Extensions
extension Color {
    /// Initialize color from hex string
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
    
    /// Safety rating colors
    static let safetyVeryHigh = Color.green
    static let safetyHigh = Color(red: 0.6, green: 0.8, blue: 0.2)
    static let safetyModerate = Color.yellow
    static let safetyLow = Color.orange
    static let safetyVeryLow = Color.red
}

// MARK: - String Extensions
extension String {
    /// Capitalize first letter
    var capitalizedFirst: String {
        return prefix(1).capitalized + dropFirst()
    }
    
    /// Remove extra whitespace
    var trimmed: String {
        return trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    /// Check if string is valid email
    var isValidEmail: Bool {
        let emailRegEx = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPred = NSPredicate(format:"SELF MATCHES %@", emailRegEx)
        return emailPred.evaluate(with: self)
    }
}

// MARK: - Array Extensions
extension Array where Element: Identifiable {
    /// Remove element by ID
    mutating func remove(withId id: Element.ID) {
        removeAll { $0.id == id }
    }
    
    /// Find element by ID
    func first(withId id: Element.ID) -> Element? {
        first { $0.id == id }
    }
}

// MARK: - View Extensions
extension View {
    /// Apply conditional modifier
    @ViewBuilder
    func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }
    
    /// Apply conditional modifier with else clause
    @ViewBuilder
    func `if`<TrueContent: View, FalseContent: View>(
        _ condition: Bool,
        if ifTransform: (Self) -> TrueContent,
        else elseTransform: (Self) -> FalseContent
    ) -> some View {
        if condition {
            ifTransform(self)
        } else {
            elseTransform(self)
        }
    }
    
    /// Add border with corner radius
    func border(_ color: Color, width: CGFloat, cornerRadius: CGFloat) -> some View {
        overlay(
            RoundedRectangle(cornerRadius: cornerRadius)
                .stroke(color, lineWidth: width)
        )
    }
    
    /// Add shadow with default values
    func defaultShadow() -> some View {
        shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    /// Add card-like appearance
    func cardStyle() -> some View {
        background(Color(.systemBackground))
            .cornerRadius(12)
            .defaultShadow()
    }
}

// MARK: - Bundle Extensions
extension Bundle {
    /// Decode JSON file from bundle
    func decode<T: Codable>(_ type: T.Type, from file: String) throws -> T {
        guard let url = self.url(forResource: file, withExtension: nil) else {
            throw BundleError.fileNotFound(file)
        }
        
        let data = try Data(contentsOf: url)
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        
        do {
            return try decoder.decode(T.self, from: data)
        } catch {
            throw BundleError.decodingFailed(error)
        }
    }
}

// MARK: - Bundle Error
enum BundleError: LocalizedError {
    case fileNotFound(String)
    case decodingFailed(Error)
    
    var errorDescription: String? {
        switch self {
        case .fileNotFound(let filename):
            return "Could not find \(filename) in bundle"
        case .decodingFailed(let error):
            return "Failed to decode file: \(error.localizedDescription)"
        }
    }
}

// MARK: - UserDefaults Extensions
extension UserDefaults {
    /// Keys for user preferences
    enum Keys {
        static let hasLaunchedBefore = "hasLaunchedBefore"
        static let preferredSafetyFilter = "preferredSafetyFilter"
        static let notificationsEnabled = "notificationsEnabled"
        static let lastDataRefresh = "lastDataRefresh"
    }
    
    /// Check if app has launched before
    var hasLaunchedBefore: Bool {
        get { bool(forKey: Keys.hasLaunchedBefore) }
        set { set(newValue, forKey: Keys.hasLaunchedBefore) }
    }
    
    /// Preferred safety filter
    var preferredSafetyFilter: SafetyRating? {
        get {
            guard let rawValue = string(forKey: Keys.preferredSafetyFilter) else { return nil }
            return SafetyRating(rawValue: rawValue)
        }
        set { set(newValue?.rawValue, forKey: Keys.preferredSafetyFilter) }
    }
    
    /// Notifications enabled
    var notificationsEnabled: Bool {
        get { bool(forKey: Keys.notificationsEnabled) }
        set { set(newValue, forKey: Keys.notificationsEnabled) }
    }
    
    /// Last data refresh date
    var lastDataRefresh: Date? {
        get { object(forKey: Keys.lastDataRefresh) as? Date }
        set { set(newValue, forKey: Keys.lastDataRefresh) }
    }
}
