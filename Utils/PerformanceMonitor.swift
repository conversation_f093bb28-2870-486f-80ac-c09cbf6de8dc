//
//  PerformanceMonitor.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import Foundation
import SwiftUI
import os.log

/// Performance monitoring and optimization utilities
final class PerformanceMonitor {
    static let shared = PerformanceMonitor()
    
    private let logger = Logger(subsystem: "com.safetravel.performance", category: "monitoring")
    private var measurements: [String: [TimeInterval]] = [:]
    private let queue = DispatchQueue(label: "performance.monitor", qos: .utility)
    
    private init() {}
    
    // MARK: - Time Measurement
    
    /// Start measuring execution time for an operation
    func startMeasurement(_ operation: String) -> PerformanceMeasurement {
        return PerformanceMeasurement(operation: operation, monitor: self)
    }
    
    /// Measure execution time of a closure
    func measure<T>(_ operation: String, block: () throws -> T) rethrows -> T {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = try block()
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        
        recordMeasurement(operation, duration: timeElapsed)
        return result
    }
    
    /// Measure execution time of an async closure
    func measureAsync<T>(_ operation: String, block: () async throws -> T) async rethrows -> T {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = try await block()
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        
        recordMeasurement(operation, duration: timeElapsed)
        return result
    }
    
    // MARK: - Memory Monitoring
    
    /// Get current memory usage
    func getCurrentMemoryUsage() -> MemoryUsage {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            let usedMemory = Int64(info.resident_size)
            let totalMemory = Int64(ProcessInfo.processInfo.physicalMemory)
            
            return MemoryUsage(
                used: usedMemory,
                total: totalMemory,
                percentage: Double(usedMemory) / Double(totalMemory) * 100
            )
        } else {
            return MemoryUsage(used: 0, total: 0, percentage: 0)
        }
    }
    
    /// Log memory warning
    func logMemoryWarning() {
        let usage = getCurrentMemoryUsage()
        logger.warning("Memory warning - Used: \(usage.formattedUsed), Percentage: \(String(format: "%.1f", usage.percentage))%")
    }
    
    // MARK: - Performance Reporting
    
    /// Get performance statistics for an operation
    func getStatistics(for operation: String) -> PerformanceStatistics? {
        queue.sync {
            guard let measurements = measurements[operation], !measurements.isEmpty else {
                return nil
            }
            
            let sorted = measurements.sorted()
            let count = measurements.count
            let sum = measurements.reduce(0, +)
            let average = sum / Double(count)
            let median = sorted[count / 2]
            let min = sorted.first!
            let max = sorted.last!
            
            return PerformanceStatistics(
                operation: operation,
                count: count,
                average: average,
                median: median,
                min: min,
                max: max,
                total: sum
            )
        }
    }
    
    /// Get all performance statistics
    func getAllStatistics() -> [PerformanceStatistics] {
        queue.sync {
            return measurements.keys.compactMap { getStatistics(for: $0) }
        }
    }
    
    /// Clear all measurements
    func clearMeasurements() {
        queue.sync {
            measurements.removeAll()
        }
    }
    
    /// Log performance summary
    func logPerformanceSummary() {
        let stats = getAllStatistics()
        
        logger.info("Performance Summary:")
        for stat in stats.sorted(by: { $0.average > $1.average }) {
            let avgMs = String(format: "%.2f", stat.average * 1000)
            logger.info("\(stat.operation): avg=\(avgMs)ms, count=\(stat.count)")
        }
    }
    
    // MARK: - Private Methods
    
    private func recordMeasurement(_ operation: String, duration: TimeInterval) {
        queue.async {
            if self.measurements[operation] == nil {
                self.measurements[operation] = []
            }
            self.measurements[operation]?.append(duration)
            
            // Keep only last 100 measurements per operation
            if let count = self.measurements[operation]?.count, count > 100 {
                self.measurements[operation]?.removeFirst(count - 100)
            }
        }
        
        // Log slow operations
        if duration > 0.1 { // 100ms threshold
            logger.warning("Slow operation: \(operation) took \(duration * 1000, specifier: \"%.2f\")ms")
        }
    }
}

// MARK: - Performance Measurement
final class PerformanceMeasurement {
    private let operation: String
    private let startTime: CFAbsoluteTime
    private let monitor: PerformanceMonitor
    
    init(operation: String, monitor: PerformanceMonitor) {
        self.operation = operation
        self.monitor = monitor
        self.startTime = CFAbsoluteTimeGetCurrent()
    }
    
    func end() {
        let duration = CFAbsoluteTimeGetCurrent() - startTime
        monitor.recordMeasurement(operation, duration: duration)
    }
}

// MARK: - Memory Usage
struct MemoryUsage {
    let used: Int64
    let total: Int64
    let percentage: Double
    
    var formattedUsed: String {
        ByteCountFormatter.string(fromByteCount: used, countStyle: .memory)
    }
    
    var formattedTotal: String {
        ByteCountFormatter.string(fromByteCount: total, countStyle: .memory)
    }
}

// MARK: - Performance Statistics
struct PerformanceStatistics {
    let operation: String
    let count: Int
    let average: TimeInterval
    let median: TimeInterval
    let min: TimeInterval
    let max: TimeInterval
    let total: TimeInterval
    
    var formattedAverage: String {
        String(format: "%.2fms", average * 1000)
    }
    
    var formattedMedian: String {
        String(format: "%.2fms", median * 1000)
    }
    
    var formattedMin: String {
        String(format: "%.2fms", min * 1000)
    }
    
    var formattedMax: String {
        String(format: "%.2fms", max * 1000)
    }
}

// MARK: - Performance View Modifier
struct PerformanceTrackingModifier: ViewModifier {
    let operation: String
    @State private var measurement: PerformanceMeasurement?
    
    func body(content: Content) -> some View {
        content
            .onAppear {
                measurement = PerformanceMonitor.shared.startMeasurement("\(operation)_appear")
            }
            .onDisappear {
                measurement?.end()
                measurement = nil
            }
    }
}

extension View {
    func trackPerformance(_ operation: String) -> some View {
        modifier(PerformanceTrackingModifier(operation: operation))
    }
}

// MARK: - Lazy Loading Helper
struct LazyView<Content: View>: View {
    let build: () -> Content
    
    init(_ build: @autoclosure @escaping () -> Content) {
        self.build = build
    }
    
    var body: Content {
        build()
    }
}

// MARK: - Performance Optimized List
struct OptimizedList<Data: RandomAccessCollection, Content: View>: View where Data.Element: Identifiable {
    let data: Data
    let content: (Data.Element) -> Content
    let batchSize: Int
    
    @State private var visibleRange: Range<Data.Index>
    @State private var scrollOffset: CGFloat = 0
    
    init(
        _ data: Data,
        batchSize: Int = 20,
        @ViewBuilder content: @escaping (Data.Element) -> Content
    ) {
        self.data = data
        self.content = content
        self.batchSize = batchSize
        
        let startIndex = data.startIndex
        let endIndex = data.index(startIndex, offsetBy: min(batchSize, data.count), limitedBy: data.endIndex) ?? data.endIndex
        self._visibleRange = State(initialValue: startIndex..<endIndex)
    }
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                ForEach(Array(data[visibleRange]), id: \.id) { item in
                    content(item)
                        .onAppear {
                            updateVisibleRange(for: item)
                        }
                }
            }
        }
    }
    
    private func updateVisibleRange(for item: Data.Element) {
        guard let itemIndex = data.firstIndex(where: { $0.id == item.id }) else { return }
        
        let currentStart = visibleRange.lowerBound
        let currentEnd = visibleRange.upperBound
        
        // Expand range if needed
        if itemIndex >= data.index(currentEnd, offsetBy: -5, limitedBy: data.startIndex) ?? data.startIndex {
            let newEnd = data.index(currentEnd, offsetBy: batchSize, limitedBy: data.endIndex) ?? data.endIndex
            visibleRange = currentStart..<newEnd
        }
    }
}

// MARK: - Image Preloader
@MainActor
final class ImagePreloader: ObservableObject {
    private let imageCache = ImageCacheManager.shared
    private var preloadTasks: [String: Task<Void, Never>] = [:]
    
    func preloadImages(urls: [String], priority: TaskPriority = .background) {
        for url in urls {
            guard preloadTasks[url] == nil else { continue }
            
            preloadTasks[url] = Task(priority: priority) {
                _ = await imageCache.loadImage(from: url)
                preloadTasks.removeValue(forKey: url)
            }
        }
    }
    
    func cancelPreloading() {
        for task in preloadTasks.values {
            task.cancel()
        }
        preloadTasks.removeAll()
    }
}

