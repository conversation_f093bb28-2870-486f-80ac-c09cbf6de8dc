//
//  AnimationHelpers.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import SwiftUI

// MARK: - Animation Extensions
extension Animation {
    /// Smooth spring animation for UI transitions
    static let smoothSpring = Animation.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0)
    
    /// Quick bounce animation for button presses
    static let quickBounce = Animation.spring(response: 0.3, dampingFraction: 0.6, blendDuration: 0)
    
    /// Gentle fade animation
    static let gentleFade = Animation.easeInOut(duration: 0.4)
    
    /// Smooth slide animation
    static let smoothSlide = Animation.easeOut(duration: 0.5)
    
    /// Snappy animation for quick interactions
    static let snappy = Animation.spring(response: 0.4, dampingFraction: 0.9, blendDuration: 0)
}

// MARK: - Transition Extensions
extension AnyTransition {
    /// Slide and fade transition
    static let slideAndFade = AnyTransition.asymmetric(
        insertion: .move(edge: .trailing).combined(with: .opacity),
        removal: .move(edge: .leading).combined(with: .opacity)
    )
    
    /// Scale and fade transition
    static let scaleAndFade = AnyTransition.scale.combined(with: .opacity)
    
    /// Slide up transition
    static let slideUp = AnyTransition.move(edge: .bottom).combined(with: .opacity)
    
    /// Slide down transition
    static let slideDown = AnyTransition.move(edge: .top).combined(with: .opacity)
    
    /// Custom card transition
    static let cardTransition = AnyTransition.asymmetric(
        insertion: .scale(scale: 0.8).combined(with: .opacity),
        removal: .scale(scale: 1.1).combined(with: .opacity)
    )
}

// MARK: - Animated Container
struct AnimatedContainer<Content: View>: View {
    let content: Content
    let animation: Animation
    @State private var isVisible = false
    
    init(animation: Animation = .smoothSpring, @ViewBuilder content: () -> Content) {
        self.animation = animation
        self.content = content()
    }
    
    var body: some View {
        content
            .opacity(isVisible ? 1 : 0)
            .scaleEffect(isVisible ? 1 : 0.95)
            .onAppear {
                withAnimation(animation) {
                    isVisible = true
                }
            }
    }
}

// MARK: - Staggered Animation
struct StaggeredAnimation<Content: View>: View {
    let content: Content
    let staggerDelay: Double
    @State private var isVisible = false
    
    init(staggerDelay: Double = 0.1, @ViewBuilder content: () -> Content) {
        self.staggerDelay = staggerDelay
        self.content = content()
    }
    
    var body: some View {
        content
            .opacity(isVisible ? 1 : 0)
            .offset(y: isVisible ? 0 : 20)
            .onAppear {
                withAnimation(.smoothSpring.delay(staggerDelay)) {
                    isVisible = true
                }
            }
    }
}

// MARK: - Bounce Button
struct BounceButton<Content: View>: View {
    let action: () -> Void
    let content: Content
    @State private var isPressed = false
    
    init(action: @escaping () -> Void, @ViewBuilder content: () -> Content) {
        self.action = action
        self.content = content()
    }
    
    var body: some View {
        Button(action: action) {
            content
        }
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.quickBounce) {
                isPressed = pressing
            }
        }, perform: {})
    }
}

// MARK: - Shimmer Effect
struct ShimmerEffect: View {
    @State private var isAnimating = false
    
    var body: some View {
        LinearGradient(
            colors: [
                Color.gray.opacity(0.3),
                Color.gray.opacity(0.1),
                Color.gray.opacity(0.3)
            ],
            startPoint: .leading,
            endPoint: .trailing
        )
        .mask(
            Rectangle()
                .fill(
                    LinearGradient(
                        colors: [Color.clear, Color.black, Color.clear],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .rotationEffect(.degrees(70))
                .offset(x: isAnimating ? 300 : -300)
        )
        .onAppear {
            withAnimation(.linear(duration: 1.5).repeatForever(autoreverses: false)) {
                isAnimating = true
            }
        }
    }
}

// MARK: - Loading Dots
struct LoadingDots: View {
    @State private var animationPhase = 0
    
    var body: some View {
        HStack(spacing: 4) {
            ForEach(0..<3, id: \.self) { index in
                Circle()
                    .fill(Color.accentColor)
                    .frame(width: 8, height: 8)
                    .scaleEffect(animationPhase == index ? 1.2 : 0.8)
                    .opacity(animationPhase == index ? 1.0 : 0.6)
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 0.6).repeatForever()) {
                animationPhase = (animationPhase + 1) % 3
            }
        }
    }
}

// MARK: - Pulse Animation
struct PulseAnimation: ViewModifier {
    @State private var isPulsing = false
    let duration: Double
    let minOpacity: Double
    let maxOpacity: Double
    
    init(duration: Double = 1.0, minOpacity: Double = 0.5, maxOpacity: Double = 1.0) {
        self.duration = duration
        self.minOpacity = minOpacity
        self.maxOpacity = maxOpacity
    }
    
    func body(content: Content) -> some View {
        content
            .opacity(isPulsing ? maxOpacity : minOpacity)
            .onAppear {
                withAnimation(.easeInOut(duration: duration).repeatForever(autoreverses: true)) {
                    isPulsing = true
                }
            }
    }
}

extension View {
    func pulse(duration: Double = 1.0, minOpacity: Double = 0.5, maxOpacity: Double = 1.0) -> some View {
        modifier(PulseAnimation(duration: duration, minOpacity: minOpacity, maxOpacity: maxOpacity))
    }
}

// MARK: - Slide In Animation
struct SlideInAnimation: ViewModifier {
    let edge: Edge
    let distance: CGFloat
    @State private var isVisible = false
    
    init(from edge: Edge, distance: CGFloat = 50) {
        self.edge = edge
        self.distance = distance
    }
    
    func body(content: Content) -> some View {
        content
            .offset(
                x: edge == .leading ? (isVisible ? 0 : -distance) : (edge == .trailing ? (isVisible ? 0 : distance) : 0),
                y: edge == .top ? (isVisible ? 0 : -distance) : (edge == .bottom ? (isVisible ? 0 : distance) : 0)
            )
            .opacity(isVisible ? 1 : 0)
            .onAppear {
                withAnimation(.smoothSpring) {
                    isVisible = true
                }
            }
    }
}

extension View {
    func slideIn(from edge: Edge, distance: CGFloat = 50) -> some View {
        modifier(SlideInAnimation(from: edge, distance: distance))
    }
}

// MARK: - Haptic Feedback Helper
struct HapticFeedback {
    static func impact(_ style: UIImpactFeedbackGenerator.FeedbackStyle = .medium) {
        let generator = UIImpactFeedbackGenerator(style: style)
        generator.impactOccurred()
    }
    
    static func notification(_ type: UINotificationFeedbackGenerator.FeedbackType) {
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(type)
    }
    
    static func selection() {
        let generator = UISelectionFeedbackGenerator()
        generator.selectionChanged()
    }
}

// MARK: - Animated Number
struct AnimatedNumber: View {
    let value: Int
    let duration: Double
    @State private var displayValue: Int = 0
    
    init(value: Int, duration: Double = 0.5) {
        self.value = value
        self.duration = duration
    }
    
    var body: some View {
        Text("\(displayValue)")
            .onAppear {
                animateToValue()
            }
            .onChange(of: value) { _, newValue in
                animateToValue()
            }
    }
    
    private func animateToValue() {
        let steps = max(1, abs(value - displayValue))
        let stepDuration = duration / Double(steps)
        
        for i in 0...steps {
            DispatchQueue.main.asyncAfter(deadline: .now() + stepDuration * Double(i)) {
                let progress = Double(i) / Double(steps)
                displayValue = Int(Double(displayValue) + Double(value - displayValue) * progress)
            }
        }
    }
}

// MARK: - Floating Action Button
struct FloatingActionButton: View {
    let action: () -> Void
    let icon: String
    let color: Color
    @State private var isPressed = false
    
    init(icon: String, color: Color = .accentColor, action: @escaping () -> Void) {
        self.icon = icon
        self.color = color
        self.action = action
    }
    
    var body: some View {
        Button(action: {
            HapticFeedback.impact(.light)
            action()
        }) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.white)
                .frame(width: 56, height: 56)
                .background(color)
                .clipShape(Circle())
                .shadow(color: color.opacity(0.3), radius: 8, x: 0, y: 4)
        }
        .scaleEffect(isPressed ? 0.9 : 1.0)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.quickBounce) {
                isPressed = pressing
            }
        }, perform: {})
    }
}
