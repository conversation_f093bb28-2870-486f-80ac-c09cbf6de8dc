//
//  Constants.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import Foundation
import SwiftUI

/// App-wide constants and configuration
enum Constants {
    
    // MARK: - App Information
    enum App {
        static let name = "SafeTravel"
        static let version = "1.0.0"
        static let buildNumber = "1"
        static let bundleIdentifier = "com.safetravel.app"
    }
    
    // MARK: - Data
    enum Data {
        static let countriesFileName = "countries.json"
        static let maxSearchResults = 50
        static let searchDebounceDelay: TimeInterval = 0.3
        static let dataRefreshInterval: TimeInterval = 24 * 60 * 60 // 24 hours

        // Performance settings
        static let listBatchSize = 20
        static let imagePreloadDistance = 5
        static let maxCachedImages = 100
        static let imageCacheSize: Int64 = 50 * 1024 * 1024 // 50MB
        static let performanceLoggingEnabled = true
        static let slowOperationThreshold: TimeInterval = 0.1 // 100ms
    }
    
    // MARK: - UI Constants
    enum UI {
        static let cornerRadius: CGFloat = 12
        static let smallCornerRadius: CGFloat = 8
        static let padding: CGFloat = 16
        static let smallPadding: CGFloat = 8
        static let iconSize: CGFloat = 24
        static let largeIconSize: CGFloat = 32
        static let cardShadowRadius: CGFloat = 2
        static let animationDuration: Double = 0.3
        static let hapticFeedbackIntensity: CGFloat = 0.7
    }
    
    // MARK: - Map Constants
    enum Map {
        static let defaultLatitude: Double = 20.0
        static let defaultLongitude: Double = 0.0
        static let defaultSpan: Double = 100.0
        static let annotationSize: CGFloat = 30
        static let clusterRadius: Double = 60.0
    }
    
    // MARK: - Notification Constants
    enum Notifications {
        static let maxNotifications = 100
        static let defaultExpiryDays = 30
        static let reminderDaysBefore = [1, 7] // Days before trip to send reminders
    }
    
    // MARK: - Safety Thresholds
    enum Safety {
        static let criticalAlertThreshold = 3 // Number of critical alerts to show warning
        static let highRiskCountryThreshold = SafetyRating.low
        static let recommendedVaccinationDays = 14 // Days before travel to get vaccinations
    }
    
    // MARK: - Colors
    enum Colors {
        // Safety Rating Colors
        static let safetyVeryHigh = Color.green
        static let safetyHigh = Color(red: 0.6, green: 0.8, blue: 0.2)
        static let safetyModerate = Color.yellow
        static let safetyLow = Color.orange
        static let safetyVeryLow = Color.red
        
        // Alert Severity Colors
        static let severityLow = Color.blue
        static let severityMedium = Color.orange
        static let severityHigh = Color.red
        static let severityCritical = Color.purple
        
        // Status Colors
        static let statusActive = Color.green
        static let statusUpcoming = Color.blue
        static let statusCompleted = Color.gray
        
        // Background Colors
        static let cardBackground = Color(.systemGray6)
        static let primaryBackground = Color(.systemBackground)
        static let secondaryBackground = Color(.secondarySystemBackground)
    }
    
    // MARK: - SF Symbols
    enum Symbols {
        // Tab Bar Icons
        static let mapTab = "map"
        static let notificationsTab = "bell"
        static let tripsTab = "suitcase"
        static let searchTab = "magnifyingglass"
        
        // Safety Icons
        static let shield = "shield"
        static let shieldFill = "shield.fill"
        static let warning = "exclamationmark.triangle"
        static let warningFill = "exclamationmark.triangle.fill"
        static let checkmark = "checkmark.circle.fill"
        
        // Trip Icons
        static let airplane = "airplane"
        static let calendar = "calendar"
        static let clock = "clock"
        static let location = "location"
        static let flag = "flag"
        
        // Health Icons
        static let cross = "cross.case"
        static let syringe = "syringe"
        static let heart = "heart"
        static let thermometer = "thermometer"
        
        // Navigation Icons
        static let chevronRight = "chevron.right"
        static let chevronDown = "chevron.down"
        static let plus = "plus"
        static let minus = "minus"
        static let xmark = "xmark"
        
        // Action Icons
        static let share = "square.and.arrow.up"
        static let edit = "pencil"
        static let delete = "trash"
        static let refresh = "arrow.clockwise"
        static let filter = "line.3.horizontal.decrease.circle"
    }
    
    // MARK: - Accessibility
    enum Accessibility {
        static let minimumTapTarget: CGFloat = 44
        static let preferredFontSizeCategory = ContentSizeCategory.large
        static let reducedMotionDuration: Double = 0.1
    }
    
    // MARK: - Network
    enum Network {
        static let timeoutInterval: TimeInterval = 30
        static let maxRetryAttempts = 3
        static let retryDelay: TimeInterval = 1
    }
    
    // MARK: - Validation
    enum Validation {
        static let minTripDuration = 1 // Minimum trip duration in days
        static let maxTripDuration = 365 // Maximum trip duration in days
        static let maxNotesLength = 500
        static let maxCountryNameLength = 100
    }
    
    // MARK: - URLs
    enum URLs {
        static let privacyPolicy = "https://safetravel.com/privacy"
        static let termsOfService = "https://safetravel.com/terms"
        static let support = "https://safetravel.com/support"
        static let feedback = "mailto:<EMAIL>"
    }
}

// MARK: - Layout Constants
extension Constants {
    enum Layout {
        // Grid Layouts
        static let countryGridColumns = [
            GridItem(.adaptive(minimum: 160, maximum: 200), spacing: UI.padding)
        ]
        
        static let statsGridColumns = [
            GridItem(.flexible()),
            GridItem(.flexible())
        ]
        
        // List Layouts
        static let listRowHeight: CGFloat = 60
        static let cardHeight: CGFloat = 120
        static let heroImageHeight: CGFloat = 200
        
        // Spacing
        static let sectionSpacing: CGFloat = 24
        static let itemSpacing: CGFloat = 12
        static let compactSpacing: CGFloat = 8
    }
}

// MARK: - Animation Constants
extension Constants {
    enum Animation {
        static let spring = SwiftUI.Animation.spring(response: 0.5, dampingFraction: 0.8)
        static let easeInOut = SwiftUI.Animation.easeInOut(duration: UI.animationDuration)
        static let bouncy = SwiftUI.Animation.bouncy(duration: 0.6)
        static let smooth = SwiftUI.Animation.smooth(duration: 0.4)
    }
}

// MARK: - Haptic Feedback
extension Constants {
    enum Haptics {
        static let light = UIImpactFeedbackGenerator.FeedbackStyle.light
        static let medium = UIImpactFeedbackGenerator.FeedbackStyle.medium
        static let heavy = UIImpactFeedbackGenerator.FeedbackStyle.heavy
        static let success = UINotificationFeedbackGenerator.FeedbackType.success
        static let warning = UINotificationFeedbackGenerator.FeedbackType.warning
        static let error = UINotificationFeedbackGenerator.FeedbackType.error
    }
}

