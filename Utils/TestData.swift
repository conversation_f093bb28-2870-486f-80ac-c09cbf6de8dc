//
//  TestData.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/31/25.
//

import Foundation
import SwiftData

/// Test data for development and testing
enum TestData {
    
    // MARK: - Sample Countries
    static let sampleCountries: [Country] = [
        japan,
        ukraine,
        unitedStates,
        france,
        thailand
    ]
    
    // MARK: - Individual Countries
    static let japan = Country(
        id: "japan",
        name: "Japan",
        flagEmoji: "🇯🇵",
        description: "Japan is renowned for its exceptional safety standards, making it one of the safest countries in the world for travelers. The country has extremely low crime rates, excellent healthcare, and reliable infrastructure.",
        photoURL: "",
        continent: "Asia",
        capital: "Tokyo",
        population: *********,
        currency: "Japanese Yen (¥)",
        language: "Japanese",
        overallSafetyRating: .veryHigh,
        alerts: [],
        healthInfo: HealthInformation(
            healthcareQuality: .excellent,
            waterSafety: .safe,
            foodSafety: .safe,
            airQuality: .good,
            emergencyNumber: "119",
            healthInsuranceRequired: false
        ),
        safetyInfo: SafetyInformation(
            crimeRate: .veryLow,
            terrorismThreat: .low,
            politicalStability: .veryStable,
            naturalDisasterRisk: .medium,
            roadSafety: .excellent,
            publicTransportSafety: .excellent,
            emergencyServices: EmergencyServices(
                police: "110",
                fire: "119",
                medical: "119",
                general: "110",
                responseTime: .fast,
                reliability: .excellent
            )
        ),
        latitude: 36.2048,
        longitude: 138.2529,
        timeZone: "Asia/Tokyo",
        visaRequired: false,
        vaccinationsRequired: []
    )
    
    static let ukraine = Country(
        id: "ukraine",
        name: "Ukraine",
        flagEmoji: "🇺🇦",
        description: "Ukraine is currently experiencing an active conflict. Travel is strongly discouraged due to ongoing military operations, infrastructure damage, and significant safety risks.",
        photoURL: "",
        continent: "Europe",
        capital: "Kyiv",
        population: 44000000,
        currency: "Ukrainian Hryvnia (₴)",
        language: "Ukrainian",
        overallSafetyRating: .veryLow,
        alerts: [
            SafetyAlert(
                id: "ukraine-war-2024",
                type: .war,
                severity: .critical,
                title: "Active Military Conflict",
                description: "Ongoing military operations throughout the country. Civilian infrastructure under attack.",
                issuedDate: Date(),
                expiryDate: nil,
                source: "Government Travel Advisory",
                affectedRegions: ["All regions"]
            )
        ],
        healthInfo: HealthInformation(
            healthcareQuality: .poor,
            waterSafety: .unsafe,
            foodSafety: .safe,
            airQuality: .moderate,
            emergencyNumber: "112",
            healthInsuranceRequired: true
        ),
        safetyInfo: SafetyInformation(
            crimeRate: .high,
            terrorismThreat: .high,
            politicalStability: .veryUnstable,
            naturalDisasterRisk: .low,
            roadSafety: .poor,
            publicTransportSafety: .poor,
            emergencyServices: EmergencyServices(
                police: "102",
                fire: "101",
                medical: "103",
                general: "112",
                responseTime: .slow,
                reliability: .poor
            )
        ),
        latitude: 48.3794,
        longitude: 31.1656,
        timeZone: "Europe/Kiev",
        visaRequired: false,
        vaccinationsRequired: ["Hepatitis A", "Hepatitis B"]
    )
    
    static let unitedStates = Country(
        id: "united-states",
        name: "United States",
        flagEmoji: "🇺🇸",
        description: "The United States offers generally good safety standards with excellent healthcare and infrastructure in most areas. Crime rates vary significantly by location.",
        photoURL: "",
        continent: "North America",
        capital: "Washington, D.C.",
        population: 331900000,
        currency: "US Dollar ($)",
        language: "English",
        overallSafetyRating: .high,
        alerts: [],
        healthInfo: HealthInformation(
            healthcareQuality: .excellent,
            waterSafety: .safe,
            foodSafety: .safe,
            airQuality: .good,
            emergencyNumber: "911",
            healthInsuranceRequired: true
        ),
        safetyInfo: SafetyInformation(
            crimeRate: .moderate,
            terrorismThreat: .low,
            politicalStability: .stable,
            naturalDisasterRisk: .medium,
            roadSafety: .good,
            publicTransportSafety: .good,
            emergencyServices: EmergencyServices(
                police: "911",
                fire: "911",
                medical: "911",
                general: "911",
                responseTime: .fast,
                reliability: .excellent
            )
        ),
        latitude: 39.8283,
        longitude: -98.5795,
        timeZone: "America/New_York",
        visaRequired: true,
        vaccinationsRequired: []
    )
    
    static let france = Country(
        id: "france",
        name: "France",
        flagEmoji: "🇫🇷",
        description: "France is a safe destination with excellent healthcare and infrastructure. Petty crime can occur in tourist areas, but violent crime is rare.",
        photoURL: "",
        continent: "Europe",
        capital: "Paris",
        population: 67400000,
        currency: "Euro (€)",
        language: "French",
        overallSafetyRating: .high,
        alerts: [],
        healthInfo: HealthInformation(
            healthcareQuality: .excellent,
            waterSafety: .safe,
            foodSafety: .safe,
            airQuality: .good,
            emergencyNumber: "112",
            healthInsuranceRequired: false
        ),
        safetyInfo: SafetyInformation(
            crimeRate: .low,
            terrorismThreat: .high,
            politicalStability: .stable,
            naturalDisasterRisk: .low,
            roadSafety: .good,
            publicTransportSafety: .excellent,
            emergencyServices: EmergencyServices(
                police: "17",
                fire: "18",
                medical: "15",
                general: "112",
                responseTime: .fast,
                reliability: .excellent
            )
        ),
        latitude: 46.2276,
        longitude: 2.2137,
        timeZone: "Europe/Paris",
        visaRequired: false,
        vaccinationsRequired: []
    )
    
    static let thailand = Country(
        id: "thailand",
        name: "Thailand",
        flagEmoji: "🇹🇭",
        description: "Thailand is generally safe for tourists with good healthcare in major cities. Some areas may have higher crime rates, and natural disasters like flooding can occur.",
        photoURL: "",
        continent: "Asia",
        capital: "Bangkok",
        population: 69800000,
        currency: "Thai Baht (฿)",
        language: "Thai",
        overallSafetyRating: .moderate,
        alerts: [],
        healthInfo: HealthInformation(
            healthcareQuality: .good,
            waterSafety: .safe,
            foodSafety: .safe,
            airQuality: .good,
            emergencyNumber: "191",
            healthInsuranceRequired: true
        ),
        safetyInfo: SafetyInformation(
            crimeRate: .moderate,
            terrorismThreat: .low,
            politicalStability: .stable,
            naturalDisasterRisk: .medium,
            roadSafety: .excellent,
            publicTransportSafety: .good,
            emergencyServices: EmergencyServices(
                police: "191",
                fire: "199",
                medical: "1669",
                general: "191",
                responseTime: .moderate,
                reliability: .good
            )
        ),
        latitude: 15.8700,
        longitude: 100.9925,
        timeZone: "Asia/Bangkok",
        visaRequired: false,
        vaccinationsRequired: ["Hepatitis A", "Typhoid"]
    )
    
    // MARK: - Sample Trips
    static let sampleTrips: [Trip] = [
        Trip(
            countryId: "japan",
            countryName: "Japan",
            countryFlag: "🇯🇵",
            startDate: Calendar.current.date(byAdding: .day, value: 30, to: Date()) ?? Date(),
            endDate: Calendar.current.date(byAdding: .day, value: 45, to: Date()) ?? Date(),
            purpose: .leisure,
            notes: "Cherry blossom season trip to Tokyo and Kyoto"
        ),
        Trip(
            countryId: "france",
            countryName: "France",
            countryFlag: "🇫🇷",
            startDate: Calendar.current.date(byAdding: .day, value: -10, to: Date()) ?? Date(),
            endDate: Calendar.current.date(byAdding: .day, value: 5, to: Date()) ?? Date(),
            purpose: .business,
            notes: "Conference in Paris"
        ),
        Trip(
            countryId: "thailand",
            countryName: "Thailand",
            countryFlag: "🇹🇭",
            startDate: Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date(),
            endDate: Calendar.current.date(byAdding: .day, value: -15, to: Date()) ?? Date(),
            purpose: .leisure,
            notes: "Beach vacation in Phuket"
        )
    ]
    
    // MARK: - Sample Notifications
    static let sampleNotifications: [TravelNotification] = [
        TravelNotification(
            title: "Trip Reminder",
            message: "Your trip to Japan starts in 7 days. Don't forget to check your passport!",
            type: .tripReminder,
            priority: .medium,
            countryId: "japan",
            countryName: "Japan"
        ),
        TravelNotification(
            title: "Safety Alert",
            message: "New travel advisory issued for Ukraine due to ongoing conflict.",
            type: .safetyAlert,
            priority: .critical,
            countryId: "ukraine",
            countryName: "Ukraine"
        ),
        TravelNotification(
            title: "Health Advisory",
            message: "Recommended vaccinations updated for Thailand travel.",
            type: .healthAdvisory,
            priority: .medium,
            countryId: "thailand",
            countryName: "Thailand"
        )
    ]
}

// MARK: - Test Data Helper
extension TestData {
    /// Create a test model container with sample data
    static func createTestContainer() throws -> ModelContainer {
        let schema = Schema([Country.self, Trip.self, TravelNotification.self])
        let configuration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: true)
        let container = try ModelContainer(for: schema, configurations: [configuration])
        
        let context = container.mainContext
        
        // Insert sample data
        for country in sampleCountries {
            context.insert(country)
        }
        
        for trip in sampleTrips {
            context.insert(trip)
        }
        
        for notification in sampleNotifications {
            context.insert(notification)
        }
        
        try context.save()
        
        return container
    }
}

