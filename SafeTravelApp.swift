//
//  SafeTravelApp.swift
//  SafeTravel
//
//  Created by <PERSON><PERSON><PERSON> on 7/30/25.
//

import SwiftUI
import SwiftData

@main
struct SafeTravelApp: App {
    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
            Country.self,
            Trip.self,
            TravelNotification.self
        ])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)

        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            fatalError("Could not create ModelContainer: \(error)")
        }
    }()

    var body: some Scene {
        WindowGroup {
            MainTabView()
        }
        .modelContainer(sharedModelContainer)
    }
}
